-- Reverse the contacts and friendships enhancements

-- Drop added indexes
DROP INDEX IF EXISTS idx_friend_requests_auto_generated;
DROP INDEX IF EXISTS idx_friend_requests_source_bubble_id;
DROP INDEX IF EXISTS idx_friendships_created_at;
DROP INDEX IF EXISTS idx_friendships_source_bubble_id;
DROP INDEX IF EXISTS idx_contacts_relationship;
DROP INDEX IF EXISTS idx_contacts_accepted_at;
DROP INDEX IF EXISTS idx_contacts_message;

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN friend_requests.auto_generated IS NULL;
COMMENT ON COLUMN friend_requests.source_bubble_id IS NULL;
COMMENT ON COLUMN friendships.source_bubble_id IS NULL;
COMMENT ON COLUMN contacts.accepted_at IS NULL;
COMMENT ON COLUMN contacts.message IS NULL;

-- Remove added columns
ALTER TABLE friend_requests DROP COLUMN IF EXISTS auto_generated;
ALTER TABLE friend_requests DROP COLUMN IF EXISTS source_bubble_id;
ALTER TABLE friendships DROP COLUMN IF EXISTS source_bubble_id;
<PERSON>TER TABLE contacts DROP COLUMN IF EXISTS accepted_at;
ALTER TABLE contacts DROP COLUMN IF EXISTS message;
