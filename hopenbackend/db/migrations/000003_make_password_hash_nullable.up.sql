-- Migration: Make password_hash nullable since we use <PERSON><PERSON> for authentication
-- This fixes the NOT NULL constraint violation when creating users

-- Make password_hash nullable
ALTER TABLE users ALTER COLUMN password_hash DROP NOT NULL;

-- Add comment to clarify that password_hash is optional when using Ory <PERSON>s
COMMENT ON COLUMN users.password_hash IS 'Password hash - nullable when using external auth providers like Ory Kratos';
