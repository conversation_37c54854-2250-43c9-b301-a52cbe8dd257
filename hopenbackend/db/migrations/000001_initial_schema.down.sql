-- Drop all indexes first
DROP INDEX IF EXISTS idx_friend_requests_status;
DROP INDEX IF EXISTS idx_friend_requests_requested_id;
DROP INDEX IF EXISTS idx_friend_requests_requester_id;

DROP INDEX IF EXISTS idx_friendships_status;
DROP INDEX IF EXISTS idx_friendships_friend_id;
DROP INDEX IF EXISTS idx_friendships_user_id;

DROP INDEX IF EXISTS idx_contacts_contact_user_id;
DROP INDEX IF EXISTS idx_contacts_user_id;

DROP INDEX IF EXISTS idx_user_reports_status;
DROP INDEX IF EXISTS idx_user_reports_reported_id;
DROP INDEX IF EXISTS idx_user_reports_reporter_id;

DROP INDEX IF EXISTS idx_user_blocks_blocked_id;
DROP INDEX IF EXISTS idx_user_blocks_blocker_id;

DROP INDEX IF EXISTS idx_user_devices_device_token;
DROP INDEX IF EXISTS idx_user_devices_user_id;

DROP INDEX IF EXISTS idx_user_sessions_is_active;
DROP INDEX IF EXISTS idx_user_sessions_session_token;
DROP INDEX IF EXISTS idx_user_sessions_user_id;

DROP INDEX IF EXISTS idx_bubble_requests_status;
DROP INDEX IF EXISTS idx_bubble_requests_requester_id;
DROP INDEX IF EXISTS idx_bubble_requests_bubble_id;

DROP INDEX IF EXISTS idx_bubble_members_status;
DROP INDEX IF EXISTS idx_bubble_members_user_id;
DROP INDEX IF EXISTS idx_bubble_members_bubble_id;

DROP INDEX IF EXISTS idx_bubbles_created_at;
DROP INDEX IF EXISTS idx_bubbles_expires_at;
DROP INDEX IF EXISTS idx_bubbles_status;
DROP INDEX IF EXISTS idx_bubbles_creator_id;

DROP INDEX IF EXISTS idx_users_created_at;
DROP INDEX IF EXISTS idx_users_is_active;
DROP INDEX IF EXISTS idx_users_username;
DROP INDEX IF EXISTS idx_users_email;

-- Drop all tables in reverse dependency order
DROP TABLE IF EXISTS friend_requests;
DROP TABLE IF EXISTS friendships;
DROP TABLE IF EXISTS contacts;
DROP TABLE IF EXISTS user_reports;
DROP TABLE IF EXISTS user_blocks;
DROP TABLE IF EXISTS user_devices;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS bubble_requests;
DROP TABLE IF EXISTS bubble_members;
DROP TABLE IF EXISTS bubbles;
DROP TABLE IF EXISTS users;

-- Drop extensions
DROP EXTENSION IF EXISTS "uuid-ossp";
