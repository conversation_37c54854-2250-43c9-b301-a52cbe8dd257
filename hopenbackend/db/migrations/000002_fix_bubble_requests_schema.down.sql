-- Reverse the bubble_requests schema changes

-- Drop indexes
DROP INDEX IF EXISTS idx_request_votes_voted_at;
DROP INDEX IF EXISTS idx_request_votes_vote;
DROP INDEX IF EXISTS idx_request_votes_voter_id;
DROP INDEX IF EXISTS idx_request_votes_request_id;

DROP INDEX IF EXISTS idx_bubble_requests_completed_at;
DROP INDEX IF EXISTS idx_bubble_requests_requires_unanimous;
DROP INDEX IF EXISTS idx_bubble_requests_target_user_id;
DROP INDEX IF EXISTS idx_bubble_requests_recipient_id;
DROP INDEX IF EXISTS idx_bubble_requests_request_type;

-- Drop the request_votes table
DROP TABLE IF EXISTS request_votes;

-- Remove added columns
ALTER TABLE bubble_requests DROP COLUMN IF EXISTS completed_at;
ALTER TABLE bubble_requests DROP COLUMN IF EXISTS requires_unanimous;
ALTER TABLE bubble_requests DROP COLUMN IF EXISTS target_user_id;
ALTER TABLE bubble_requests DROP COLUMN IF EXISTS recipient_id;
ALTER TABLE bubble_requests DROP COLUMN IF EXISTS request_type;

-- Restore original constraints
ALTER TABLE bubble_requests DROP CONSTRAINT IF EXISTS bubble_requests_status_check;
ALTER TABLE bubble_requests ADD CONSTRAINT bubble_requests_status_check 
    CHECK (status IN ('pending', 'approved', 'rejected', 'expired'));

ALTER TABLE bubble_requests DROP CONSTRAINT IF EXISTS bubble_requests_request_type_check;

-- Make bubble_id NOT NULL again
ALTER TABLE bubble_requests ALTER COLUMN bubble_id SET NOT NULL;
