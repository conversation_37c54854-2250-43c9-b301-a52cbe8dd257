# --- Build stage
FROM golang:1.23-alpine AS builder
ENV GO111MODULE=on
WORKDIR /app

# Install git for private modules if needed
RUN apk add --no-cache git

# Copy module files and download dependencies to leverage Docker cache
COPY go.mod go.sum ./
# Download dependencies
RUN go mod download

# Copy the rest of the source code
COPY . .

# Compile the binary statically for minimal image
RUN CGO_ENABLED=0 go build -o hopenbackend ./cmd/main.go

# --- Run stage
FROM alpine:3.19

# Add certificates and timezone data. Only add other dependencies if absolutely necessary for runtime.
RUN apk --no-cache add ca-certificates tzdata

WORKDIR /app

# Copy binary and required assets (config files, migrations, certificates)
COPY --from=builder /app/hopenbackend /app/hopenbackend
RUN chmod +x /app/hopenbackend
COPY config.yaml ./config.yaml
COPY migrations ./migrations # Consider running migrations outside the app container
COPY certs ./ssl # For production, mount certs as secrets instead of copying them

EXPOSE 4000 8080 # Expose all relevant ports

# The application binary should be the entrypoint
ENTRYPOINT ["/app/hopenbackend"]