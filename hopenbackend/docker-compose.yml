services:
  # PostgreSQL Database
  postgresql:
    image: postgres:15-alpine
    container_name: hopen_postgresql
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:5432:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-hopen_db}
      POSTGRES_USER: ${POSTGRES_USER:-hopen}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-hopen123}
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./migrations/postgresql:/docker-entrypoint-initdb.d
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-hopen} -d ${POSTGRES_DB:-hopen_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # ArangoDB Database - REMOVED (migrated to PostgreSQL)

  # Cassandra Database
  cassandra:
    image: cassandra:4.1
    container_name: hopen_cassandra
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:9042:9042"
    environment:
      CASSANDRA_CLUSTER_NAME: hopen_cluster
      CASSANDRA_DC: datacenter1
      CASSANDRA_RACK: rack1
      CASSANDRA_ENDPOINT_SNITCH: GossipingPropertyFileSnitch
      CASSANDRA_NUM_TOKENS: 128
    volumes:
      - cassandra_data:/var/lib/cassandra
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD-SHELL", "cqlsh -e 'describe cluster'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Valkey (Redis-compatible) Cache
  valkey:
    image: valkey/valkey:7.2-alpine
    container_name: hopen_valkey
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:6379:6379"
    command: valkey-server --appendonly yes --requirepass hopen123
    volumes:
      - valkey_data:/data
    networks:
      - hopen_network

  # NATS JetStream
  nats:
    image: nats:2.10-alpine
    container_name: hopen_nats
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:4222:4222"
      - "${DOCKER_HOST_IP:-0.0.0.0}:8222:8222"
      - "${DOCKER_HOST_IP:-0.0.0.0}:6222:6222"
    command: [
      "--jetstream",
      "--store_dir=/data",
      "--http_port=8222",
      "--port=4222",
      "--cluster_name=hopen_cluster"
    ]
    volumes:
      - nats_data:/data
    networks:
      - hopen_network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: hopen_minio
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:9000:9000"
      - "${DOCKER_HOST_IP:-0.0.0.0}:9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - hopen_network

  # EMQX MQTT5 Broker
  emqx:
    image: emqx/emqx:5.4.1
    container_name: hopen_emqx
    restart: unless-stopped
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:1883:1883"      # MQTT
      - "${DOCKER_HOST_IP:-0.0.0.0}:8883:8883"      # MQTT over SSL
      - "${DOCKER_HOST_IP:-0.0.0.0}:8083:8083"      # WebSocket
      - "${DOCKER_HOST_IP:-0.0.0.0}:8084:8084"      # WebSocket over SSL
      - "${DOCKER_HOST_IP:-0.0.0.0}:18083:18083"    # Dashboard & API (HTTP/3 + HTTP/2)
    environment:
      # Node configuration
      - EMQX_NAME=emqx
      - EMQX_HOST=emqx
      
      # Dashboard configuration
      - EMQX_DASHBOARD__DEFAULT_USERNAME=${EMQX_DASHBOARD__DEFAULT_USERNAME}
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=${EMQX_DASHBOARD__DEFAULT_PASSWORD}

      # Cluster configuration
      - EMQX_CLUSTER__DISCOVERY_STRATEGY=manual
      - EMQX_CLUSTER__NAME=hopen_cluster
      
      # HTTP/3 + HTTP/2 support (PRIMARY: HTTP/3, FALLBACK: HTTP/2)
      - EMQX_LISTENERS__HTTP__DASHBOARD__BIND=18083
      - EMQX_LISTENERS__HTTP__DASHBOARD__HTTP3=true
      - EMQX_LISTENERS__HTTP__DASHBOARD__HTTP2=true
      - EMQX_LISTENERS__HTTP__DASHBOARD__TLS__ENABLE=true
      - EMQX_LISTENERS__HTTP__DASHBOARD__TLS__CERT_FILE=/etc/ssl/certs/emqx.crt
      - EMQX_LISTENERS__HTTP__DASHBOARD__TLS__KEY_FILE=/etc/ssl/private/emqx.key
      - EMQX_LISTENERS__HTTP__DASHBOARD__TLS__VERSIONS=tlsv1.3,tlsv1.2
      - EMQX_LISTENERS__HTTP__DASHBOARD__TLS__ALPN=h3,h2,http/1.1
      
      # Security
      - EMQX_MQTT__ALLOW_ANONYMOUS=false
      - EMQX_MQTT__ACL_NOMATCH=deny
      
      # Performance
      - EMQX_MQTT__MAX_PACKET_SIZE=1MB
      - EMQX_MQTT__MAX_MQUEUE_LEN=1000
      - EMQX_MQTT__SESSION_EXPIRY_INTERVAL=2h
      - EMQX_MQTT__MAX_INFLIGHT=32
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
      - ./config/emqx.conf:/opt/emqx/etc/emqx.conf:ro
      - ./certs:/etc/ssl/certs:ro
      - ./certs:/etc/ssl/private:ro
    networks:
      - hopen_network
    depends_on:
      - postgresql

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: hopen_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - hopen_network

  # Grafana (Dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: hopen_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: hopen123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - hopen_network

  # Hopen Backend API server
  backend:
    build: .
    container_name: hopen_backend
    depends_on:
      - postgresql
      - cassandra
      - valkey
      - nats
      - minio
      - emqx
      - kratos
    environment:
      - HOPEN_APP_PORT=${HOPEN_APP_PORT:-4000}
      - DOCKER_HOST_IP=${DOCKER_HOST_IP}
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:4000:4000"   # Main API port
    volumes:
      - ./certs:/app/certs:ro  # Mount SSL certificates
    networks:
      - hopen_network
    restart: unless-stopped

  kratos:
    image: oryd/kratos:v1.1.0
    container_name: hopen_kratos
    restart: unless-stopped
    depends_on:
      - postgresql
    environment:
      - DSN=postgres://${POSTGRES_USER:-hopen}:${POSTGRES_PASSWORD:-hopen123}@postgresql:5432/${POSTGRES_DB:-hopen_db}?sslmode=disable
      - KRATOS_LOG_LEVEL=${KRATOS_LOG_LEVEL:-info}
      - KRATOS_CONFIG=/etc/config/kratos.yml
    volumes:
      - ./config/kratos:/etc/config
    command: serve --dev --config /etc/config/kratos.yml
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:4433:4433"    # public
      - "${DOCKER_HOST_IP:-0.0.0.0}:4434:4434"    # admin
    networks:
      - hopen_network

  hydra:
    image: oryd/hydra:v1.11.2
    container_name: hopen_hydra
    restart: unless-stopped
    depends_on:
      - postgresql
    environment:
      # Database configuration
      - DSN=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/hydra_db?sslmode=disable

      # HTTP/3 PRIMARY with HTTP/2 fallback URLs (NO HTTP/1.1)
      - URLS_SELF_ISSUER=https://${DOCKER_HOST_IP}:4444/
      - URLS_LOGIN=https://${DOCKER_HOST_IP}:3000/login
      - URLS_CONSENT=https://${DOCKER_HOST_IP}:3000/consent
      - URLS_LOGOUT=https://${DOCKER_HOST_IP}:3000/logout
      
      # Security configuration
      - SECRETS_SYSTEM=${SECRETS_SYSTEM}
      - SECRETS_COOKIE=${SECRETS_COOKIE}
      
      # Auto-migration and logging
      - HYDRA_AUTO_MIGRATE=${HYDRA_AUTO_MIGRATE}
      - HYDRA_LOG_LEVEL=${HYDRA_LOG_LEVEL}
      - HYDRA_LOG_FORMAT=${HYDRA_LOG_FORMAT}
      
      # HTTP/3 + HTTP/2 TLS configuration (NO HTTP/1.1)
      - HYDRA_SERVE_TLS_ALLOW_TERMINATION_FROM=0.0.0.0/0
      - HYDRA_SERVE_TLS_TERMINATION=0.0.0.0/0
      - HYDRA_SERVE_TLS_CERT_FILE=/etc/ssl/certs/hydra.crt
      - HYDRA_SERVE_TLS_KEY_FILE=/etc/ssl/private/hydra.key
      
      # HTTP/3 Protocol Support
      - HYDRA_SERVE_TLS_ALPN_PROTOCOLS=h3,h2
      - HYDRA_SERVE_TLS_MIN_VERSION=1.3
      
      # Performance and security
      - HYDRA_SERVE_PUBLIC_CORS_ENABLED=true
      - HYDRA_SERVE_PUBLIC_CORS_ALLOWED_ORIGINS=https://${DOCKER_HOST_IP}:3000
      - HYDRA_SERVE_ADMIN_CORS_ENABLED=true
      - HYDRA_SERVE_ADMIN_CORS_ALLOWED_ORIGINS=https://${DOCKER_HOST_IP}:3000

      # OAuth2 configuration
      - HYDRA_OAUTH2_EXPOSE_INTERNAL_ERRORS=true
      - HYDRA_OAUTH2_ISSUER_URL=https://${DOCKER_HOST_IP}:4444/
      
      # Session configuration
      - HYDRA_SERVE_COOKIE_SAME_SITE_MODE=Lax
      - HYDRA_SERVE_COOKIE_SAME_SITE_LEGACY_WORKAROUND=false
    volumes:
      - ./certs:/etc/ssl/certs:ro
      - ./certs:/etc/ssl/private:ro
    command: serve all
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:4444:4444"  # public (HTTP/3 + HTTP/2)
      - "${DOCKER_HOST_IP:-0.0.0.0}:4445:4445"  # admin (HTTP/3 + HTTP/2)
    networks:
      - hopen_network

volumes:
  postgresql_data:
  cassandra_data:
  valkey_data:
  nats_data:
  minio_data:
  emqx_data:
  emqx_log:
  prometheus_data:
  grafana_data:

networks:
  hopen_network:
    driver: bridge
