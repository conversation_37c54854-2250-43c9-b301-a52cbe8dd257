-- Migration: Create contacts and friendships tables
-- This migration creates PostgreSQL tables to replace ArangoDB collections

-- Create contacts table (replaces ArangoDB contacts collection)
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id VARCHAR(255) NOT NULL, -- References users(id)
    recipient_id VARCHAR(255) NOT NULL, -- References users(id)
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'accepted', 'declined'
    message TEXT, -- Optional message with contact request
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create friendships table (replaces ArangoDB friendships collection)
CREATE TABLE IF NOT EXISTS friendships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user1_id VARCHAR(255) NOT NULL, -- References users(id)
    user2_id VARCHAR(255) NOT NULL, -- References users(id)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_bubble_id UUID, -- References bubbles(id) - bubble that expired to create friendship
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create friend_requests table (replaces ArangoDB friend_requests collection)
CREATE TABLE IF NOT EXISTS friend_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id VARCHAR(255) NOT NULL, -- References users(id)
    recipient_id VARCHAR(255) NOT NULL, -- References users(id)
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'accepted', 'declined'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_bubble_id UUID, -- References bubbles(id) - bubble expiry that triggered this
    auto_generated BOOLEAN DEFAULT TRUE, -- always true - no manual friend requests
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for optimal query performance

-- Contacts table indexes
CREATE INDEX IF NOT EXISTS idx_contacts_requester_id ON contacts(requester_id);
CREATE INDEX IF NOT EXISTS idx_contacts_recipient_id ON contacts(recipient_id);
CREATE INDEX IF NOT EXISTS idx_contacts_status ON contacts(status);
CREATE INDEX IF NOT EXISTS idx_contacts_created_at ON contacts(created_at);
-- Composite index for checking existing relationships
CREATE INDEX IF NOT EXISTS idx_contacts_relationship ON contacts(requester_id, recipient_id, status);

-- Friendships table indexes
CREATE INDEX IF NOT EXISTS idx_friendships_user1_id ON friendships(user1_id);
CREATE INDEX IF NOT EXISTS idx_friendships_user2_id ON friendships(user2_id);
CREATE INDEX IF NOT EXISTS idx_friendships_created_at ON friendships(created_at);
CREATE INDEX IF NOT EXISTS idx_friendships_source_bubble ON friendships(source_bubble_id);
-- Composite index for finding friendships between users
CREATE INDEX IF NOT EXISTS idx_friendships_users ON friendships(user1_id, user2_id);

-- Friend requests table indexes
CREATE INDEX IF NOT EXISTS idx_friend_requests_requester_id ON friend_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_friend_requests_recipient_id ON friend_requests(recipient_id);
CREATE INDEX IF NOT EXISTS idx_friend_requests_status ON friend_requests(status);
CREATE INDEX IF NOT EXISTS idx_friend_requests_created_at ON friend_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_friend_requests_source_bubble ON friend_requests(source_bubble_id);

-- Add constraints to prevent duplicate relationships

-- Prevent duplicate contact requests between same users
CREATE UNIQUE INDEX IF NOT EXISTS idx_contacts_unique_relationship 
ON contacts(LEAST(requester_id, recipient_id), GREATEST(requester_id, recipient_id))
WHERE status IN ('pending', 'accepted');

-- Prevent duplicate friendships between same users
CREATE UNIQUE INDEX IF NOT EXISTS idx_friendships_unique_relationship 
ON friendships(LEAST(user1_id, user2_id), GREATEST(user1_id, user2_id));

-- Prevent duplicate friend requests between same users for same bubble
CREATE UNIQUE INDEX IF NOT EXISTS idx_friend_requests_unique_bubble_relationship 
ON friend_requests(LEAST(requester_id, recipient_id), GREATEST(requester_id, recipient_id), source_bubble_id)
WHERE status = 'pending';

-- Add check constraints
ALTER TABLE contacts ADD CONSTRAINT chk_contacts_status 
CHECK (status IN ('pending', 'accepted', 'declined'));

ALTER TABLE contacts ADD CONSTRAINT chk_contacts_different_users 
CHECK (requester_id != recipient_id);

ALTER TABLE friendships ADD CONSTRAINT chk_friendships_different_users 
CHECK (user1_id != user2_id);

ALTER TABLE friend_requests ADD CONSTRAINT chk_friend_requests_status 
CHECK (status IN ('pending', 'accepted', 'declined'));

ALTER TABLE friend_requests ADD CONSTRAINT chk_friend_requests_different_users 
CHECK (requester_id != recipient_id);

-- Add updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to automatically update updated_at columns
CREATE TRIGGER update_contacts_updated_at 
    BEFORE UPDATE ON contacts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_friendships_updated_at 
    BEFORE UPDATE ON friendships 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_friend_requests_updated_at
    BEFORE UPDATE ON friend_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE contacts IS 'Contact relationships and requests between users';
COMMENT ON TABLE friendships IS 'Established friendships between users, typically created from expired bubbles';
COMMENT ON TABLE friend_requests IS 'Auto-generated friend requests from bubble expiry events';

COMMENT ON COLUMN contacts.status IS 'Status of contact request: pending, accepted, declined';
COMMENT ON COLUMN contacts.message IS 'Optional message included with contact request';
COMMENT ON COLUMN friendships.source_bubble_id IS 'Bubble that expired to create this friendship';
COMMENT ON COLUMN friend_requests.auto_generated IS 'Always true - no manual friend requests allowed';
COMMENT ON COLUMN friend_requests.source_bubble_id IS 'Bubble expiry event that triggered this friend request';
