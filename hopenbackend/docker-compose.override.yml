services:
  # Backend service with proper SSL certificates
  backend:
    volumes:
      - ./certs/hopen-dev.crt:/app/certs/server.crt:ro
      - ./certs/hopen-dev.key:/app/certs/server.key:ro
    environment:
      - SSL_CERT_PATH=/app/certs/server.crt
      - SSL_KEY_PATH=/app/certs/server.key
      - ENABLE_HTTPS=true
      - ENABLE_HTTP3=true
      - TLS_DOMAIN=hopen.local
      - DOCKER_HOST_IP=*********
    ports:
      - "*********:4000:4000"  # Main API port
