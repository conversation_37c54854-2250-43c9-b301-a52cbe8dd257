// Include the mandatory Flutter header file.
#include <flutter/runtime_effect.glsl>

// Declare uniforms that Flutter will provide.
// We replace <PERSON><PERSON><PERSON>'s `iResolution` with `u_size`.
uniform vec2 u_size;
// We replace <PERSON><PERSON><PERSON>'s `iTime` with `u_time`.
uniform float u_time;

// Declare the output color variable for Flutter.
out vec4 frag_color;

// The main function for Flutter shaders is always `void main()`.
void main() {
    // Get the pixel coordinate from Flutter. This replaces `fragCoord`.
    vec2 fragCoord = FlutterFragCoord().xy;

    // --- The original shader logic starts here ---

    if (int(fragCoord.x) % 4 == 0) {
        frag_color = vec4(0.0, 0.0, 0.0, 1.0);
    } else if (int(fragCoord.y) % 4 == 0) {
        frag_color = vec4(0.0, 0.0, 0.0, 1.0);
    } else {
        // Use `u_size` instead of `iResolution.xy`.
        vec2 uv = fragCoord / u_size;

        // Use `u_time` instead of `iTime`.
        vec3 col = 0.5 + 0.5 * cos(u_time + uv.xyx + vec3(0, 2, 4));

        if (int(fragCoord.x) % 4 == 1) {
            // Output the final color to `frag_color` instead of `fragColor`.
            frag_color = vec4(col.x, 0.0, 0.0, 1.0);
        } else if (int(fragCoord.x) % 4 == 2) {
            frag_color = vec4(0.0, col.y, 0.0, 1.0);
        } else if (int(fragCoord.x) % 4 == 3) {
            frag_color = vec4(0.0, 0.0, col.z, 1.0);
        }
    }
}