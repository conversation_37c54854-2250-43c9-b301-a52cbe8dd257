import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// Provider for managing shader resources throughout the app
class ShaderProvider extends InheritedWidget {
  const ShaderProvider({
    super.key,
    required this.shaderCollection,
    required super.child,
  });

  final ShaderCollection shaderCollection;

  static ShaderCollection of(BuildContext context) {
    final provider = context.dependOnInheritedWidgetOfExactType<ShaderProvider>();
    if (provider == null) {
      throw FlutterError(
        'ShaderProvider not found in widget tree. '
        'Make sure to wrap your app with ShaderProvider.',
      );
    }
    return provider.shaderCollection;
  }

  static ShaderCollection? maybeOf(BuildContext context) {
    final provider = context.dependOnInheritedWidgetOfExactType<ShaderProvider>();
    return provider?.shaderCollection;
  }

  @override
  bool updateShouldNotify(covariant ShaderProvider oldWidget) {
    return oldWidget.shaderCollection != shaderCollection;
  }
}

/// Collection of loaded shader programs
class ShaderCollection {
  const ShaderCollection({
    required this.rgbShader,
  });

  final ui.FragmentProgram rgbShader;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShaderCollection && other.rgbShader == rgbShader;
  }

  @override
  int get hashCode => rgbShader.hashCode;
}

/// Service for loading shader programs
class ShaderService {
  static Future<ShaderCollection> loadShaders() async {
    try {
      print('🎨 Loading RGB shader...');
      final rgbShader = await ui.FragmentProgram.fromAsset(
        'lib/presentation/shaders/rgb.frag',
      );
      print('✅ RGB shader loaded successfully');

      return ShaderCollection(rgbShader: rgbShader);
    } catch (e) {
      print('❌ Failed to load shaders: $e');
      rethrow;
    }
  }
}
