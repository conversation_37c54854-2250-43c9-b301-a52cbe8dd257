import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Service to handle Firebase initialization with proper error handling and state management
class FirebaseInitializationService {
  static final FirebaseInitializationService _instance = FirebaseInitializationService._internal();
  factory FirebaseInitializationService() => _instance;
  FirebaseInitializationService._internal();

  bool _isInitialized = false;
  bool _initializationFailed = false;
  String? _initializationError;
  final Completer<bool> _initializationCompleter = Completer<bool>();

  /// Check if Firebase is initialized and ready to use
  bool get isInitialized => _isInitialized;

  /// Check if Firebase initialization failed
  bool get initializationFailed => _initializationFailed;

  /// Get the initialization error if any
  String? get initializationError => _initializationError;

  /// Wait for Firebase initialization to complete
  Future<bool> waitForInitialization() async {
    if (_initializationCompleter.isCompleted) {
      return _isInitialized;
    }
    return await _initializationCompleter.future;
  }

  /// Initialize Firebase with proper error handling
  Future<bool> initialize({FirebaseOptions? options}) async {
    if (_initializationCompleter.isCompleted) {
      return _isInitialized;
    }

    try {
      if (kIsWeb && options != null) {
        // Web configuration with explicit options
        await Firebase.initializeApp(options: options);
      } else {
        // Mobile platforms - use default configuration
        await Firebase.initializeApp();
      }

      _isInitialized = true;
      _initializationFailed = false;
      _initializationError = null;

      if (kDebugMode) {
        print('✅ Firebase initialized successfully');
      }

      _initializationCompleter.complete(true);
      return true;
    } catch (e) {
      _isInitialized = false;
      _initializationFailed = true;
      _initializationError = e.toString();

      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }

      _initializationCompleter.complete(false);
      return false;
    }
  }

  /// Check if Firebase apps are available (alternative check)
  bool get hasFirebaseApps => Firebase.apps.isNotEmpty;

  /// Get Firebase initialization status for debugging
  Map<String, dynamic> get status => {
        'isInitialized': _isInitialized,
        'initializationFailed': _initializationFailed,
        'initializationError': _initializationError,
        'hasFirebaseApps': hasFirebaseApps,
        'firebaseAppsCount': Firebase.apps.length,
      };

  /// Reset the service state (for testing purposes)
  void reset() {
    _isInitialized = false;
    _initializationFailed = false;
    _initializationError = null;
    // Note: Cannot reset the completer as it can only be completed once
  }
}
