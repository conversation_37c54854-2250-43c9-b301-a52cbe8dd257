import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../di/injection_container_refactored.dart' as di;
import '../../provider/notifiers/user_profile_notifier.dart';
import '../../provider/repositories/user/cached_user_repository.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';

/// Service responsible for ensuring complete user data isolation
/// 
/// This service implements OWASP security best practices for session management
/// by ensuring that all user-specific data is completely cleared when a user
/// logs out or when switching between different user accounts.
/// 
/// Key security principles implemented:
/// 1. Complete data isolation between users
/// 2. Clear all cached data on logout
/// 3. Prevent data leakage between user sessions
/// 4. Follow principle of least privilege for data persistence
class UserDataIsolationService {
  static const String _tag = 'UserDataIsolationService';
  
  /// Comprehensive cleanup of all user-specific data
  /// 
  /// This method should be called:
  /// - When a user logs out
  /// - When switching between different user accounts
  /// - When detecting potential session hijacking
  static Future<void> clearAllUserData({
    String? reason = 'User logout',
  }) async {
    LoggingService.info('$_tag: Starting comprehensive user data cleanup - Reason: $reason');
    
    final List<String> clearingResults = [];
    
    // 1. Clear user profile notifier and cache
    try {
      final userProfileNotifier = di.sl<UserProfileNotifier>();
      userProfileNotifier.clearUser();
      clearingResults.add('✅ User profile notifier cleared');
    } catch (e) {
      clearingResults.add('❌ Error clearing user profile notifier: $e');
      LoggingService.error('$_tag: Error clearing user profile notifier: $e');
    }
    
    // 2. Clear cached user repository (Drift database)
    try {
      final cachedUserRepository = di.sl<CachedUserRepository>();
      await cachedUserRepository.clearCache();
      clearingResults.add('✅ Cached user repository cleared');
    } catch (e) {
      clearingResults.add('❌ Error clearing cached user repository: $e');
      LoggingService.error('$_tag: Error clearing cached user repository: $e');
    }
    
    // 3. Clear all Drift database tables
    try {
      final database = di.sl<AppDatabase>();
      await _clearAllDriftTables(database);
      clearingResults.add('✅ All Drift database tables cleared');
    } catch (e) {
      clearingResults.add('❌ Error clearing Drift database: $e');
      LoggingService.error('$_tag: Error clearing Drift database: $e');
    }
    
    // 4. Clear SharedPreferences (except app-level settings)
    try {
      await _clearUserSpecificSharedPreferences();
      clearingResults.add('✅ User-specific SharedPreferences cleared');
    } catch (e) {
      clearingResults.add('❌ Error clearing SharedPreferences: $e');
      LoggingService.error('$_tag: Error clearing SharedPreferences: $e');
    }
    
    // 5. Clear Flutter Secure Storage
    try {
      await _clearSecureStorage();
      clearingResults.add('✅ Secure storage cleared');
    } catch (e) {
      clearingResults.add('❌ Error clearing secure storage: $e');
      LoggingService.error('$_tag: Error clearing secure storage: $e');
    }
    
    // Log summary
    LoggingService.info('$_tag: User data cleanup completed. Results:');
    for (final result in clearingResults) {
      LoggingService.info('$_tag: $result');
    }
    
    if (kDebugMode) {
      debugPrint('🧹 $reason - User data cleanup completed:');
      for (final result in clearingResults) {
        debugPrint('🧹 $result');
      }
    }
  }
  
  /// Clear all Drift database tables that contain user-specific data
  static Future<void> _clearAllDriftTables(AppDatabase database) async {
    try {
      // Clear user profiles
      await database.userProfileDao.clearAllProfiles();
      
      // Clear profile pictures cache
      await database.profilePictureDao.clearAllCache();
      
      // Clear other user-specific tables
      // Add more table clearing as needed based on your app's data structure
      
      LoggingService.info('$_tag: All Drift database tables cleared successfully');
    } catch (e) {
      LoggingService.error('$_tag: Error clearing Drift database tables: $e');
      rethrow;
    }
  }
  
  /// Clear user-specific SharedPreferences while preserving app-level settings
  static Future<void> _clearUserSpecificSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // List of keys that should be preserved (app-level settings)
      const preservedKeys = {
        'app_version',
        'first_launch',
        'language_preference',
        'theme_preference',
        'onboarding_completed',
        // Add other app-level keys that should persist across user sessions
      };
      
      // Get all keys and remove user-specific ones
      final allKeys = prefs.getKeys();
      final keysToRemove = allKeys.where((key) => !preservedKeys.contains(key));
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      LoggingService.info('$_tag: Cleared ${keysToRemove.length} user-specific SharedPreferences keys');
    } catch (e) {
      LoggingService.error('$_tag: Error clearing SharedPreferences: $e');
      rethrow;
    }
  }
  
  /// Clear all Flutter Secure Storage
  static Future<void> _clearSecureStorage() async {
    try {
      const secureStorage = FlutterSecureStorage();
      await secureStorage.deleteAll();
      LoggingService.info('$_tag: Secure storage cleared successfully');
    } catch (e) {
      LoggingService.error('$_tag: Error clearing secure storage: $e');
      rethrow;
    }
  }
  
  /// Verify that user data has been properly cleared
  /// This can be used for testing and debugging purposes
  static Future<Map<String, bool>> verifyDataClearing() async {
    final results = <String, bool>{};
    
    try {
      // Check if user profile notifier is cleared
      final userProfileNotifier = di.sl<UserProfileNotifier>();
      results['user_profile_notifier_cleared'] = userProfileNotifier.currentUser == null;
      
      // Check if Drift database is cleared
      final database = di.sl<AppDatabase>();
      final userProfiles = await database.userProfileDao.getAllProfiles();
      results['drift_user_profiles_cleared'] = userProfiles.isEmpty;
      
      // Check if secure storage is cleared
      const secureStorage = FlutterSecureStorage();
      final allSecureData = await secureStorage.readAll();
      results['secure_storage_cleared'] = allSecureData.isEmpty;
      
    } catch (e) {
      LoggingService.error('$_tag: Error verifying data clearing: $e');
    }
    
    return results;
  }
}
