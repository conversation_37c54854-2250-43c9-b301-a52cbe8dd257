import 'dart:async';
import 'dart:convert';
import 'package:workmanager/workmanager.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'request_dialog_state_manager.dart';
import 'mqtt_message_persistence.dart';
import 'logging_service.dart';
import '../models/pending_dialog_model.dart';

/// Background processor for handling pending requests when app is not active
/// 
/// This service ensures that request dialogs are not lost when:
/// - App is terminated by the system
/// - App is backgrounded for extended periods
/// - User doesn't see notifications immediately
/// - Network issues prevent real-time delivery
class BackgroundRequestProcessor {
  static const String _checkPendingRequestsTask = 'check_pending_requests';
  static const String _processUnprocessedMessagesTask = 'process_unprocessed_messages';
  static const String _cleanupExpiredDataTask = 'cleanup_expired_data';

  static FlutterLocalNotificationsPlugin? _notificationsPlugin;
  static bool _isInitialized = false;

  /// Initialize the background processor
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info('BackgroundRequestProcessor: Initializing...');

      // Initialize WorkManager
      await Workmanager().initialize(
        _backgroundTaskDispatcher,
        isInDebugMode: false, // Set to false in production
      );

      // Initialize local notifications
      await _initializeNotifications();

      // Schedule periodic tasks
      await _schedulePeriodicTasks();

      _isInitialized = true;
      LoggingService.success('BackgroundRequestProcessor: Initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Failed to initialize: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Schedule periodic background tasks
  static Future<void> _schedulePeriodicTasks() async {
    try {
      // Check for pending requests every 15 minutes
      await Workmanager().registerPeriodicTask(
        _checkPendingRequestsTask,
        _checkPendingRequestsTask,
        frequency: const Duration(minutes: 15),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

      // Process unprocessed MQTT messages every 30 minutes
      await Workmanager().registerPeriodicTask(
        _processUnprocessedMessagesTask,
        _processUnprocessedMessagesTask,
        frequency: const Duration(minutes: 30),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

      // Cleanup expired data daily
      await Workmanager().registerPeriodicTask(
        _cleanupExpiredDataTask,
        _cleanupExpiredDataTask,
        frequency: const Duration(hours: 24),
        constraints: Constraints(
          networkType: NetworkType.unmetered,
          requiresBatteryNotLow: true,
          requiresCharging: false,
          requiresDeviceIdle: true,
          requiresStorageNotLow: true,
        ),
      );

      LoggingService.info('BackgroundRequestProcessor: Scheduled periodic tasks');
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Failed to schedule tasks: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Initialize local notifications for background alerts
  static Future<void> _initializeNotifications() async {
    try {
      _notificationsPlugin = FlutterLocalNotificationsPlugin();

      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notificationsPlugin!.initialize(initSettings);

      LoggingService.info('BackgroundRequestProcessor: Notifications initialized');
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Failed to initialize notifications: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Schedule a notification for a pending request
  static Future<void> scheduleRequestNotification(PendingDialog dialog) async {
    if (_notificationsPlugin == null) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'request_notifications',
        'Request Notifications',
        channelDescription: 'Notifications for pending requests',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        autoCancel: true,
        category: AndroidNotificationCategory.social,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        categoryIdentifier: 'request_category',
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final title = _getNotificationTitle(dialog.requestType);
      final body = _getNotificationBody(dialog.requestType, dialog.data);

      await _notificationsPlugin!.show(
        dialog.requestId.hashCode,
        title,
        body,
        notificationDetails,
        payload: jsonEncode({
          'request_id': dialog.requestId,
          'request_type': dialog.requestType,
          'data': dialog.data,
        }),
      );

      LoggingService.info(
        'BackgroundRequestProcessor: Scheduled notification for request: ${dialog.requestId}',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Failed to schedule notification: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Cancel all background tasks
  static Future<void> cancelAllTasks() async {
    try {
      await Workmanager().cancelAll();
      LoggingService.info('BackgroundRequestProcessor: Cancelled all background tasks');
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Failed to cancel tasks: $e',
        stackTrace: stackTrace,
      );
    }
  }

  // Private helper methods

  static String _getNotificationTitle(String requestType) {
    switch (requestType) {
      case 'contact_request_received':
        return 'New Contact Request';
      case 'friend_request_received':
        return 'New Friend Request';
      case 'bubble_start_request_received':
        return 'New Bubble Invitation';
      case 'bubble_join_request_received':
        return 'Bubble Join Request';
      default:
        return 'New Request';
    }
  }

  static String _getNotificationBody(String requestType, Map<String, dynamic> data) {
    final senderName = data['senderName'] ?? data['sender_name'] ?? 'Someone';
    
    switch (requestType) {
      case 'contact_request_received':
        return '$senderName wants to connect with you';
      case 'friend_request_received':
        return '$senderName sent you a friend request';
      case 'bubble_start_request_received':
        final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? 'a bubble';
        return '$senderName invited you to join $bubbleName';
      case 'bubble_join_request_received':
        final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? 'your bubble';
        return '$senderName wants to join $bubbleName';
      default:
        return '$senderName sent you a request';
    }
  }
}

/// Background task dispatcher - entry point for all background tasks
@pragma('vm:entry-point')
void _backgroundTaskDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      LoggingService.info('BackgroundRequestProcessor: Executing task: $task');

      switch (task) {
        case BackgroundRequestProcessor._checkPendingRequestsTask:
          return await _checkPendingRequestsTask();
        
        case BackgroundRequestProcessor._processUnprocessedMessagesTask:
          return await _processUnprocessedMessagesTask();
        
        case BackgroundRequestProcessor._cleanupExpiredDataTask:
          return await _cleanupExpiredDataTask();
        
        default:
          LoggingService.warning('BackgroundRequestProcessor: Unknown task: $task');
          return false;
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'BackgroundRequestProcessor: Task execution failed: $e',
        stackTrace: stackTrace,
      );
      return false;
    }
  });
}

/// Check for pending requests and schedule notifications
Future<bool> _checkPendingRequestsTask() async {
  try {
    LoggingService.info('BackgroundRequestProcessor: Checking for pending requests...');

    // Initialize services
    await RequestDialogStateManager.initialize();

    // Get pending dialogs
    final pendingDialogs = await RequestDialogStateManager.getPendingDialogs();

    LoggingService.info(
      'BackgroundRequestProcessor: Found ${pendingDialogs.length} pending dialogs',
    );

    // Schedule notifications for pending dialogs
    for (final dialog in pendingDialogs) {
      await BackgroundRequestProcessor.scheduleRequestNotification(dialog);
    }

    return true;
  } catch (e, stackTrace) {
    LoggingService.error(
      'BackgroundRequestProcessor: Failed to check pending requests: $e',
      stackTrace: stackTrace,
    );
    return false;
  }
}

/// Process unprocessed MQTT messages
Future<bool> _processUnprocessedMessagesTask() async {
  try {
    LoggingService.info('BackgroundRequestProcessor: Processing unprocessed messages...');

    // Get unprocessed messages
    final unprocessedMessages = await MqttMessagePersistence.getUnprocessedMessages();

    LoggingService.info(
      'BackgroundRequestProcessor: Found ${unprocessedMessages.length} unprocessed messages',
    );

    // Process each message
    for (final message in unprocessedMessages) {
      try {
        // Parse message payload
        final data = jsonDecode(message.payload) as Map<String, dynamic>;
        final type = data['type'] as String?;

        if (type != null) {
          // Handle as request notification if applicable
          if (_isRequestNotification(type)) {
            await _handleBackgroundRequestNotification(type, data);
          }

          // Mark message as processed
          await MqttMessagePersistence.markMessageProcessed(message.id);
        }
      } catch (e) {
        // Increment retry count for failed message
        await MqttMessagePersistence.incrementMessageRetry(message.id);
        LoggingService.error(
          'BackgroundRequestProcessor: Failed to process message ${message.id}: $e',
        );
      }
    }

    return true;
  } catch (e, stackTrace) {
    LoggingService.error(
      'BackgroundRequestProcessor: Failed to process unprocessed messages: $e',
      stackTrace: stackTrace,
    );
    return false;
  }
}

/// Cleanup expired data
Future<bool> _cleanupExpiredDataTask() async {
  try {
    LoggingService.info('BackgroundRequestProcessor: Cleaning up expired data...');

    // Initialize services
    await RequestDialogStateManager.initialize();

    // Get current metrics before cleanup
    final dialogMetrics = await RequestDialogStateManager.getDialogMetrics();
    final messageMetrics = await MqttMessagePersistence.getMessageMetrics();

    // Cleanup happens automatically when getting pending dialogs/messages
    await RequestDialogStateManager.getPendingDialogs();
    await MqttMessagePersistence.getUnprocessedMessages();

    LoggingService.info(
      'BackgroundRequestProcessor: Cleanup completed. Dialog metrics: $dialogMetrics, Message metrics: $messageMetrics',
    );

    return true;
  } catch (e, stackTrace) {
    LoggingService.error(
      'BackgroundRequestProcessor: Failed to cleanup expired data: $e',
      stackTrace: stackTrace,
    );
    return false;
  }
}

/// Check if a notification type is a request
bool _isRequestNotification(String type) {
  const requestTypes = {
    'contact_request_received',
    'bubble_start_request_received',
    'bubble_join_request_received',
    'friend_request_received',
  };
  return requestTypes.contains(type);
}

/// Handle request notification in background
Future<void> _handleBackgroundRequestNotification(String type, Map<String, dynamic> data) async {
  try {
    final requestId = _extractRequestId(data);
    if (requestId.isEmpty) return;

    // Check if dialog has already been shown
    final hasBeenShown = await RequestDialogStateManager.hasDialogBeenShown(requestId, type);
    if (hasBeenShown) return;

    // Add to pending dialogs
    await RequestDialogStateManager.addPendingDialog(
      requestId,
      type,
      data,
      priority: _getDialogPriority(type),
      expiresAt: DateTime.now().add(const Duration(hours: 24)),
    );

    LoggingService.info(
      'BackgroundRequestProcessor: Added background request to pending dialogs: $requestId ($type)',
    );
  } catch (e, stackTrace) {
    LoggingService.error(
      'BackgroundRequestProcessor: Failed to handle background request: $e',
      stackTrace: stackTrace,
    );
  }
}

/// Extract request ID from notification data
String _extractRequestId(Map<String, dynamic> data) {
  return (data['requestId'] ?? 
          data['request_id'] ?? 
          data['id'] ?? 
          data['notificationId'] ?? 
          data['notification_id'] ?? '').toString();
}

/// Get dialog priority based on notification type
DialogPriority _getDialogPriority(String type) {
  switch (type) {
    case 'contact_request_received':
      return DialogPriority.high;
    case 'friend_request_received':
      return DialogPriority.high;
    case 'bubble_start_request_received':
      return DialogPriority.normal;
    case 'bubble_join_request_received':
      return DialogPriority.normal;
    default:
      return DialogPriority.normal;
  }
}
