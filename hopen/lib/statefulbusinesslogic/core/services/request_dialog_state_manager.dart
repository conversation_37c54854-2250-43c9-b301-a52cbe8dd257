import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/pending_dialog_model.dart';
import 'logging_service.dart';

/// Manages persistent state for request dialogs
/// 
/// This service ensures that request dialogs are not lost when:
/// - A<PERSON> crashes during dialog display
/// - A<PERSON> is killed by the system
/// - Network issues prevent MQTT delivery
/// - User dismisses app before seeing dialog
class RequestDialogStateManager {
  static const String _shownDialogsKey = 'shown_request_dialogs';
  static const String _pendingDialogsKey = 'pending_request_dialogs';
  static const String _dialogMetricsKey = 'dialog_metrics';
  
  // Cache for performance
  static Set<String>? _shownDialogsCache;
  static List<PendingDialog>? _pendingDialogsCache;
  static bool _isInitialized = false;

  /// Initialize the service and load cached data
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      LoggingService.info('RequestDialogStateManager: Initializing...');
      
      // Load shown dialogs cache
      _shownDialogsCache = await _loadShownDialogs();
      
      // Load pending dialogs cache
      _pendingDialogsCache = await _loadPendingDialogs();
      
      // Clean up expired dialogs
      await _cleanupExpiredDialogs();
      
      _isInitialized = true;
      LoggingService.success(
        'RequestDialogStateManager: Initialized with ${_shownDialogsCache?.length ?? 0} shown dialogs and ${_pendingDialogsCache?.length ?? 0} pending dialogs',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to initialize: $e',
        stackTrace: stackTrace,
      );
      // Initialize with empty caches on error
      _shownDialogsCache = <String>{};
      _pendingDialogsCache = <PendingDialog>[];
      _isInitialized = true;
    }
  }

  /// Mark a dialog as shown to prevent duplicate displays
  static Future<void> markDialogShown(String requestId, String requestType) async {
    await _ensureInitialized();
    
    try {
      final dialogKey = _createDialogKey(requestId, requestType);
      
      // Add to cache
      _shownDialogsCache!.add(dialogKey);
      
      // Persist to storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_shownDialogsKey, _shownDialogsCache!.toList());
      
      LoggingService.info(
        'RequestDialogStateManager: Marked dialog as shown: $dialogKey',
      );
      
      // Update metrics
      await _updateDialogMetrics('shown', requestType);
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to mark dialog as shown: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if a dialog has already been shown
  static Future<bool> hasDialogBeenShown(String requestId, String requestType) async {
    await _ensureInitialized();
    
    final dialogKey = _createDialogKey(requestId, requestType);
    final hasBeenShown = _shownDialogsCache!.contains(dialogKey);
    
    LoggingService.debug(
      'RequestDialogStateManager: Dialog $dialogKey has been shown: $hasBeenShown',
    );
    
    return hasBeenShown;
  }

  /// Add a pending dialog that needs to be shown
  static Future<void> addPendingDialog(
    String requestId,
    String requestType,
    Map<String, dynamic> data, {
    DialogPriority priority = DialogPriority.normal,
    DateTime? expiresAt,
  }) async {
    await _ensureInitialized();
    
    try {
      // Check if dialog already exists
      final existingIndex = _pendingDialogsCache!.indexWhere(
        (dialog) => dialog.requestId == requestId && dialog.requestType == requestType,
      );
      
      final pendingDialog = PendingDialog(
        requestId: requestId,
        requestType: requestType,
        data: data,
        timestamp: DateTime.now(),
        priority: priority,
        expiresAt: expiresAt,
      );
      
      if (existingIndex >= 0) {
        // Update existing dialog
        _pendingDialogsCache![existingIndex] = pendingDialog;
        LoggingService.info(
          'RequestDialogStateManager: Updated existing pending dialog: $requestId',
        );
      } else {
        // Add new dialog
        _pendingDialogsCache!.add(pendingDialog);
        LoggingService.info(
          'RequestDialogStateManager: Added new pending dialog: $requestId',
        );
      }
      
      // Sort by priority and timestamp
      _pendingDialogsCache!.sort((a, b) {
        final priorityComparison = a.priority.sortOrder.compareTo(b.priority.sortOrder);
        if (priorityComparison != 0) return priorityComparison;
        return a.timestamp.compareTo(b.timestamp);
      });
      
      // Persist to storage
      await _savePendingDialogs();
      
      // Update metrics
      await _updateDialogMetrics('pending', requestType);
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to add pending dialog: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Get all pending dialogs that need to be shown
  static Future<List<PendingDialog>> getPendingDialogs() async {
    await _ensureInitialized();
    
    // Filter out invalid dialogs
    final validDialogs = _pendingDialogsCache!.where((dialog) => dialog.isValid).toList();
    
    LoggingService.debug(
      'RequestDialogStateManager: Retrieved ${validDialogs.length} valid pending dialogs',
    );
    
    return validDialogs;
  }

  /// Remove a pending dialog (after it's been shown)
  static Future<void> removePendingDialog(String requestId, String requestType) async {
    await _ensureInitialized();
    
    try {
      final initialCount = _pendingDialogsCache!.length;
      _pendingDialogsCache!.removeWhere(
        (dialog) => dialog.requestId == requestId && dialog.requestType == requestType,
      );
      
      final removedCount = initialCount - _pendingDialogsCache!.length;
      if (removedCount > 0) {
        await _savePendingDialogs();
        LoggingService.info(
          'RequestDialogStateManager: Removed $removedCount pending dialog(s) for $requestId',
        );
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to remove pending dialog: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Increment retry count for a pending dialog
  static Future<void> incrementDialogRetry(String requestId, String requestType) async {
    await _ensureInitialized();
    
    try {
      final dialogIndex = _pendingDialogsCache!.indexWhere(
        (dialog) => dialog.requestId == requestId && dialog.requestType == requestType,
      );
      
      if (dialogIndex >= 0) {
        _pendingDialogsCache![dialogIndex] = _pendingDialogsCache![dialogIndex].withIncrementedRetry();
        await _savePendingDialogs();
        
        LoggingService.info(
          'RequestDialogStateManager: Incremented retry count for dialog: $requestId',
        );
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to increment dialog retry: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Get dialog metrics for monitoring
  static Future<Map<String, dynamic>> getDialogMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_dialogMetricsKey);
      
      if (metricsJson != null) {
        return jsonDecode(metricsJson) as Map<String, dynamic>;
      }
    } catch (e) {
      LoggingService.error('RequestDialogStateManager: Failed to get metrics: $e');
    }
    
    return <String, dynamic>{};
  }

  /// Clear all dialog state (for testing or user logout)
  static Future<void> clearAllDialogState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_shownDialogsKey);
      await prefs.remove(_pendingDialogsKey);
      await prefs.remove(_dialogMetricsKey);
      
      _shownDialogsCache?.clear();
      _pendingDialogsCache?.clear();
      
      LoggingService.info('RequestDialogStateManager: Cleared all dialog state');
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to clear dialog state: $e',
        stackTrace: stackTrace,
      );
    }
  }

  // Private helper methods

  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  static String _createDialogKey(String requestId, String requestType) {
    return '${requestType}_$requestId';
  }

  static Future<Set<String>> _loadShownDialogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final shownDialogs = prefs.getStringList(_shownDialogsKey) ?? <String>[];
      return shownDialogs.toSet();
    } catch (e) {
      LoggingService.error('RequestDialogStateManager: Failed to load shown dialogs: $e');
      return <String>{};
    }
  }

  static Future<List<PendingDialog>> _loadPendingDialogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingDialogsJson = prefs.getStringList(_pendingDialogsKey) ?? <String>[];
      
      return pendingDialogsJson
          .map((json) {
            try {
              final data = jsonDecode(json) as Map<String, dynamic>;
              return PendingDialog.fromMap(data);
            } catch (e) {
              LoggingService.error('RequestDialogStateManager: Failed to parse pending dialog: $e');
              return null;
            }
          })
          .where((dialog) => dialog != null)
          .cast<PendingDialog>()
          .toList();
    } catch (e) {
      LoggingService.error('RequestDialogStateManager: Failed to load pending dialogs: $e');
      return <PendingDialog>[];
    }
  }

  static Future<void> _savePendingDialogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingDialogsJson = _pendingDialogsCache!
          .map((dialog) => jsonEncode(dialog.toMap()))
          .toList();
      
      await prefs.setStringList(_pendingDialogsKey, pendingDialogsJson);
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestDialogStateManager: Failed to save pending dialogs: $e',
        stackTrace: stackTrace,
      );
    }
  }

  static Future<void> _cleanupExpiredDialogs() async {
    try {
      final initialCount = _pendingDialogsCache!.length;
      _pendingDialogsCache!.removeWhere((dialog) => !dialog.isValid);
      
      final removedCount = initialCount - _pendingDialogsCache!.length;
      if (removedCount > 0) {
        await _savePendingDialogs();
        LoggingService.info(
          'RequestDialogStateManager: Cleaned up $removedCount expired/invalid dialogs',
        );
      }
    } catch (e) {
      LoggingService.error('RequestDialogStateManager: Failed to cleanup expired dialogs: $e');
    }
  }

  static Future<void> _updateDialogMetrics(String action, String requestType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_dialogMetricsKey);
      
      Map<String, dynamic> metrics = {};
      if (metricsJson != null) {
        metrics = jsonDecode(metricsJson) as Map<String, dynamic>;
      }
      
      final key = '${action}_$requestType';
      metrics[key] = (metrics[key] as int? ?? 0) + 1;
      metrics['last_updated'] = DateTime.now().toIso8601String();
      
      await prefs.setString(_dialogMetricsKey, jsonEncode(metrics));
    } catch (e) {
      LoggingService.error('RequestDialogStateManager: Failed to update metrics: $e');
    }
  }
}
