import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'logging_service.dart';

/// Handles persistence of unprocessed MQTT messages for reliability
/// 
/// This service ensures that MQTT messages are not lost when:
/// - Network connectivity is poor
/// - A<PERSON> is backgrounded during message processing
/// - MQTT connection is temporarily lost
/// - Message processing fails temporarily
class MqttMessagePersistence {
  static const String _unprocessedMessagesKey = 'unprocessed_mqtt_messages';
  static const String _messageMetricsKey = 'mqtt_message_metrics';
  static const int _maxStoredMessages = 100; // Limit stored messages
  static const Duration _messageExpiry = Duration(hours: 24); // Message expiry time

  /// Store an unprocessed MQTT message
  static Future<void> storeUnprocessedMessage(
    String topic,
    String payload, {
    int priority = 0,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingMessages = await _loadUnprocessedMessages();

      final message = UnprocessedMessage(
        id: _generateMessageId(),
        topic: topic,
        payload: payload,
        timestamp: DateTime.now(),
        priority: priority,
        retryCount: 0,
      );

      // Add new message
      existingMessages.add(message);

      // Sort by priority and timestamp
      existingMessages.sort((a, b) {
        final priorityComparison = b.priority.compareTo(a.priority);
        if (priorityComparison != 0) return priorityComparison;
        return a.timestamp.compareTo(b.timestamp);
      });

      // Limit stored messages
      if (existingMessages.length > _maxStoredMessages) {
        existingMessages.removeRange(_maxStoredMessages, existingMessages.length);
      }

      // Save to storage
      await _saveUnprocessedMessages(existingMessages);

      LoggingService.info(
        'MqttMessagePersistence: Stored unprocessed message for topic: $topic',
      );

      // Update metrics
      await _updateMetrics('stored', topic);
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to store unprocessed message: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Get all unprocessed messages
  static Future<List<UnprocessedMessage>> getUnprocessedMessages() async {
    try {
      final messages = await _loadUnprocessedMessages();
      
      // Filter out expired messages
      final validMessages = messages.where((message) => !message.isExpired).toList();
      
      // If we filtered out expired messages, save the cleaned list
      if (validMessages.length != messages.length) {
        await _saveUnprocessedMessages(validMessages);
        LoggingService.info(
          'MqttMessagePersistence: Cleaned up ${messages.length - validMessages.length} expired messages',
        );
      }

      LoggingService.debug(
        'MqttMessagePersistence: Retrieved ${validMessages.length} unprocessed messages',
      );

      return validMessages;
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to get unprocessed messages: $e',
        stackTrace: stackTrace,
      );
      return <UnprocessedMessage>[];
    }
  }

  /// Mark a message as processed and remove it
  static Future<void> markMessageProcessed(String messageId) async {
    try {
      final messages = await _loadUnprocessedMessages();
      final initialCount = messages.length;
      
      messages.removeWhere((message) => message.id == messageId);
      
      if (messages.length < initialCount) {
        await _saveUnprocessedMessages(messages);
        LoggingService.info(
          'MqttMessagePersistence: Marked message as processed: $messageId',
        );
        
        // Update metrics
        await _updateMetrics('processed', '');
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to mark message as processed: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Increment retry count for a message
  static Future<void> incrementMessageRetry(String messageId) async {
    try {
      final messages = await _loadUnprocessedMessages();
      final messageIndex = messages.indexWhere((message) => message.id == messageId);
      
      if (messageIndex >= 0) {
        messages[messageIndex] = messages[messageIndex].withIncrementedRetry();
        await _saveUnprocessedMessages(messages);
        
        LoggingService.info(
          'MqttMessagePersistence: Incremented retry count for message: $messageId',
        );
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to increment message retry: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Get message processing metrics
  static Future<Map<String, dynamic>> getMessageMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_messageMetricsKey);
      
      if (metricsJson != null) {
        return jsonDecode(metricsJson) as Map<String, dynamic>;
      }
    } catch (e) {
      LoggingService.error('MqttMessagePersistence: Failed to get metrics: $e');
    }
    
    return <String, dynamic>{};
  }

  /// Clear all unprocessed messages
  static Future<void> clearAllMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_unprocessedMessagesKey);
      await prefs.remove(_messageMetricsKey);
      
      LoggingService.info('MqttMessagePersistence: Cleared all unprocessed messages');
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to clear messages: $e',
        stackTrace: stackTrace,
      );
    }
  }

  // Private helper methods

  static Future<List<UnprocessedMessage>> _loadUnprocessedMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList(_unprocessedMessagesKey) ?? <String>[];
      
      return messagesJson
          .map((json) {
            try {
              final data = jsonDecode(json) as Map<String, dynamic>;
              return UnprocessedMessage.fromMap(data);
            } catch (e) {
              LoggingService.error('MqttMessagePersistence: Failed to parse message: $e');
              return null;
            }
          })
          .where((message) => message != null)
          .cast<UnprocessedMessage>()
          .toList();
    } catch (e) {
      LoggingService.error('MqttMessagePersistence: Failed to load messages: $e');
      return <UnprocessedMessage>[];
    }
  }

  static Future<void> _saveUnprocessedMessages(List<UnprocessedMessage> messages) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = messages
          .map((message) => jsonEncode(message.toMap()))
          .toList();
      
      await prefs.setStringList(_unprocessedMessagesKey, messagesJson);
    } catch (e, stackTrace) {
      LoggingService.error(
        'MqttMessagePersistence: Failed to save messages: $e',
        stackTrace: stackTrace,
      );
    }
  }

  static String _generateMessageId() {
    return 'msg_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  static Future<void> _updateMetrics(String action, String topic) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_messageMetricsKey);
      
      Map<String, dynamic> metrics = {};
      if (metricsJson != null) {
        metrics = jsonDecode(metricsJson) as Map<String, dynamic>;
      }
      
      metrics[action] = (metrics[action] as int? ?? 0) + 1;
      if (topic.isNotEmpty) {
        final topicKey = '${action}_$topic';
        metrics[topicKey] = (metrics[topicKey] as int? ?? 0) + 1;
      }
      metrics['last_updated'] = DateTime.now().toIso8601String();
      
      await prefs.setString(_messageMetricsKey, jsonEncode(metrics));
    } catch (e) {
      LoggingService.error('MqttMessagePersistence: Failed to update metrics: $e');
    }
  }
}

/// Model representing an unprocessed MQTT message
class UnprocessedMessage {
  const UnprocessedMessage({
    required this.id,
    required this.topic,
    required this.payload,
    required this.timestamp,
    this.priority = 0,
    this.retryCount = 0,
    this.maxRetries = 3,
  });

  factory UnprocessedMessage.fromMap(Map<String, dynamic> map) => UnprocessedMessage(
        id: map['id'] as String,
        topic: map['topic'] as String,
        payload: map['payload'] as String,
        timestamp: DateTime.parse(map['timestamp'] as String),
        priority: map['priority'] as int? ?? 0,
        retryCount: map['retry_count'] as int? ?? 0,
        maxRetries: map['max_retries'] as int? ?? 3,
      );

  final String id;
  final String topic;
  final String payload;
  final DateTime timestamp;
  final int priority;
  final int retryCount;
  final int maxRetries;

  /// Check if this message has expired
  bool get isExpired => DateTime.now().difference(timestamp) > MqttMessagePersistence._messageExpiry;

  /// Check if we've exceeded retry attempts
  bool get hasExceededRetries => retryCount >= maxRetries;

  /// Check if this message should still be processed
  bool get isValid => !isExpired && !hasExceededRetries;

  /// Create a copy with incremented retry count
  UnprocessedMessage withIncrementedRetry() => UnprocessedMessage(
        id: id,
        topic: topic,
        payload: payload,
        timestamp: timestamp,
        priority: priority,
        retryCount: retryCount + 1,
        maxRetries: maxRetries,
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'topic': topic,
        'payload': payload,
        'timestamp': timestamp.toIso8601String(),
        'priority': priority,
        'retry_count': retryCount,
        'max_retries': maxRetries,
      };

  @override
  String toString() => 'UnprocessedMessage('
      'id: $id, '
      'topic: $topic, '
      'priority: $priority, '
      'retryCount: $retryCount, '
      'isValid: $isValid)';
}
