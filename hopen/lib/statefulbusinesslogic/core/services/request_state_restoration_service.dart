import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'request_dialog_state_manager.dart';
import 'logging_service.dart';
import '../models/pending_dialog_model.dart';

/// Service for handling Flutter state restoration for request dialogs
///
/// This service ensures that request dialog state is preserved and restored when:
/// - App is restored by the system after being terminated
/// - User navigates back to the app after system restoration
/// - App state is restored from background termination
/// - System-level app recovery scenarios
class RequestStateRestorationService {
  static const String _restorationScopeId = 'request_dialog_restoration';
  static const String _pendingRequestsKey = 'pending_requests_restoration';
  static const String _lastShownRequestKey = 'last_shown_request_restoration';
  static const String _restorationMetricsKey = 'restoration_metrics';

  static bool _isInitialized = false;
  static RestorationBucket? _restorationBucket;

  /// Initialize the state restoration service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info('RequestStateRestorationService: Initializing...');

      // Initialize restoration bucket
      _restorationBucket = RestorationBucket.empty(
        restorationId: _restorationScopeId,
        debugOwner: 'RequestStateRestorationService',
      );

      _isInitialized = true;
      LoggingService.success(
        'RequestStateRestorationService: Initialized successfully',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to initialize: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Save current request state for restoration
  static Future<void> saveRequestState() async {
    if (!_isInitialized || _restorationBucket == null) {
      LoggingService.warning(
        'RequestStateRestorationService: Not initialized, skipping state save',
      );
      return;
    }

    try {
      LoggingService.debug(
        'RequestStateRestorationService: Saving request state...',
      );

      // Get current pending dialogs
      final pendingDialogs =
          await RequestDialogStateManager.getPendingDialogs();

      // Prepare restoration data
      final restorationData = {
        'pending_dialogs':
            pendingDialogs.map((dialog) => dialog.toMap()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
      };

      // Save to restoration bucket
      _restorationBucket!.write(
        _pendingRequestsKey,
        jsonEncode(restorationData),
      );

      LoggingService.info(
        'RequestStateRestorationService: Saved ${pendingDialogs.length} pending dialogs for restoration',
      );

      // Update metrics
      await _updateRestorationMetrics('saved', pendingDialogs.length);
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to save request state: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Restore request state from saved data
  static Future<void> restoreRequestState() async {
    if (!_isInitialized || _restorationBucket == null) {
      LoggingService.warning(
        'RequestStateRestorationService: Not initialized, skipping state restoration',
      );
      return;
    }

    try {
      LoggingService.info(
        'RequestStateRestorationService: Restoring request state...',
      );

      // Read restoration data
      final restorationDataJson = _restorationBucket!.read<String>(
        _pendingRequestsKey,
      );
      if (restorationDataJson == null) {
        LoggingService.info(
          'RequestStateRestorationService: No restoration data found',
        );
        return;
      }

      final restorationData =
          jsonDecode(restorationDataJson) as Map<String, dynamic>;
      final pendingDialogsData =
          restorationData['pending_dialogs'] as List<dynamic>;

      LoggingService.info(
        'RequestStateRestorationService: Found ${pendingDialogsData.length} dialogs to restore',
      );

      // Restore pending dialogs
      int restoredCount = 0;
      for (final dialogData in pendingDialogsData) {
        try {
          final dialog = PendingDialog.fromMap(
            dialogData as Map<String, dynamic>,
          );

          // Only restore valid, non-expired dialogs
          if (dialog.isValid) {
            // Check if dialog hasn't already been shown
            final hasBeenShown =
                await RequestDialogStateManager.hasDialogBeenShown(
                  dialog.requestId,
                  dialog.requestType,
                );

            if (!hasBeenShown) {
              await RequestDialogStateManager.addPendingDialog(
                dialog.requestId,
                dialog.requestType,
                dialog.data,
                priority: dialog.priority,
                expiresAt: dialog.expiresAt,
              );
              restoredCount++;
            }
          }
        } catch (e) {
          LoggingService.error(
            'RequestStateRestorationService: Failed to restore dialog: $e',
          );
        }
      }

      LoggingService.success(
        'RequestStateRestorationService: Restored $restoredCount pending dialogs',
      );

      // Update metrics
      await _updateRestorationMetrics('restored', restoredCount);

      // Clear restoration data after successful restoration
      _restorationBucket!.remove(_pendingRequestsKey);
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to restore request state: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Save the last shown request for tracking
  static Future<void> saveLastShownRequest(
    String requestId,
    String requestType,
  ) async {
    if (!_isInitialized || _restorationBucket == null) return;

    try {
      final lastShownData = {
        'request_id': requestId,
        'request_type': requestType,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _restorationBucket!.write(
        _lastShownRequestKey,
        jsonEncode(lastShownData),
      );

      LoggingService.debug(
        'RequestStateRestorationService: Saved last shown request: $requestId',
      );
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to save last shown request: $e',
      );
    }
  }

  /// Get the last shown request
  static Future<Map<String, String>?> getLastShownRequest() async {
    if (!_isInitialized || _restorationBucket == null) return null;

    try {
      final lastShownJson = _restorationBucket!.read<String>(
        _lastShownRequestKey,
      );
      if (lastShownJson == null) return null;

      final lastShownData = jsonDecode(lastShownJson) as Map<String, dynamic>;
      return {
        'request_id': lastShownData['request_id'] as String,
        'request_type': lastShownData['request_type'] as String,
        'timestamp': lastShownData['timestamp'] as String,
      };
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to get last shown request: $e',
      );
      return null;
    }
  }

  /// Check if restoration is needed (app was restored from termination)
  static Future<bool> isRestorationNeeded() async {
    if (!_isInitialized || _restorationBucket == null) return false;

    try {
      final restorationDataJson = _restorationBucket!.read<String>(
        _pendingRequestsKey,
      );
      return restorationDataJson != null;
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to check restoration need: $e',
      );
      return false;
    }
  }

  /// Get restoration metrics
  static Future<Map<String, dynamic>> getRestorationMetrics() async {
    try {
      if (_restorationBucket == null) return <String, dynamic>{};

      final metricsJson = _restorationBucket!.read<String>(
        _restorationMetricsKey,
      );
      if (metricsJson != null) {
        return jsonDecode(metricsJson) as Map<String, dynamic>;
      }
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to get metrics: $e',
      );
    }

    return <String, dynamic>{};
  }

  /// Clear all restoration data
  static Future<void> clearRestorationData() async {
    if (!_isInitialized || _restorationBucket == null) return;

    try {
      _restorationBucket!.remove(_pendingRequestsKey);
      _restorationBucket!.remove(_lastShownRequestKey);
      _restorationBucket!.remove(_restorationMetricsKey);

      LoggingService.info(
        'RequestStateRestorationService: Cleared all restoration data',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to clear restoration data: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Dispose of the service
  static void dispose() {
    try {
      _restorationBucket?.dispose();
      _restorationBucket = null;
      _isInitialized = false;

      LoggingService.info('RequestStateRestorationService: Disposed');
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Error during disposal: $e',
      );
    }
  }

  // Private helper methods

  static Future<void> _updateRestorationMetrics(
    String action,
    int count,
  ) async {
    try {
      if (_restorationBucket == null) return;

      final metricsJson = _restorationBucket!.read<String>(
        _restorationMetricsKey,
      );

      Map<String, dynamic> metrics = {};
      if (metricsJson != null) {
        metrics = jsonDecode(metricsJson) as Map<String, dynamic>;
      }

      metrics[action] = (metrics[action] as int? ?? 0) + count;
      metrics['last_${action}_timestamp'] = DateTime.now().toIso8601String();

      _restorationBucket!.write(_restorationMetricsKey, jsonEncode(metrics));
    } catch (e) {
      LoggingService.error(
        'RequestStateRestorationService: Failed to update metrics: $e',
      );
    }
  }
}

/// Mixin for widgets that need request state restoration
mixin RequestStateRestorationMixin<T extends StatefulWidget>
    on State<T>, RestorationMixin<T> {
  late RestorableBool _hasUnshownRequests;
  late RestorableStringN _lastShownRequestId;

  @override
  void initState() {
    super.initState();
    _hasUnshownRequests = RestorableBool(false);
    _lastShownRequestId = RestorableStringN(null);
  }

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    registerForRestoration(_hasUnshownRequests, 'has_unshown_requests');
    registerForRestoration(_lastShownRequestId, 'last_shown_request_id');

    if (initialRestore && _hasUnshownRequests.value) {
      // Schedule request recovery after restoration
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _recoverPendingRequests();
      });
    }
  }

  @override
  void dispose() {
    _hasUnshownRequests.dispose();
    _lastShownRequestId.dispose();
    super.dispose();
  }

  /// Override this method to handle request recovery
  Future<void> _recoverPendingRequests() async {
    try {
      LoggingService.info(
        'RequestStateRestorationMixin: Recovering pending requests...',
      );

      // Restore request state
      await RequestStateRestorationService.restoreRequestState();

      // Reset restoration flag
      _hasUnshownRequests.value = false;
    } catch (e, stackTrace) {
      LoggingService.error(
        'RequestStateRestorationMixin: Failed to recover pending requests: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Mark that there are unshown requests
  void markHasUnshownRequests() {
    _hasUnshownRequests.value = true;
  }

  /// Update last shown request ID
  void updateLastShownRequestId(String requestId) {
    _lastShownRequestId.value = requestId;
  }

  @override
  String? get restorationId;
}
