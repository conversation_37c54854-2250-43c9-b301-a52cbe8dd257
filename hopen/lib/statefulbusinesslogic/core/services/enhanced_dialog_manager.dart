import 'dart:async';
import 'package:flutter/material.dart';
import 'request_dialog_state_manager.dart';
import 'request_state_restoration_service.dart';
import 'background_request_processor.dart';
import 'logging_service.dart';
import '../models/pending_dialog_model.dart';

/// Enhanced dialog manager that provides comprehensive request dialog handling
/// 
/// This manager integrates all persistence and recovery mechanisms to ensure
/// that request dialogs are never lost and always shown to users when appropriate.
/// 
/// Features:
/// - Persistent dialog state tracking
/// - Duplicate dialog prevention
/// - Priority-based dialog ordering
/// - Background processing integration
/// - State restoration support
/// - Comprehensive error handling
class EnhancedDialogManager {
  static final Map<String, bool> _activeDialogs = <String, bool>{};
  static final Map<String, DateTime> _dialogTimestamps = <String, DateTime>{};
  static const Duration _dialogCooldown = Duration(seconds: 30); // Prevent rapid duplicates

  /// Show a request dialog with full persistence and recovery support
  static Future<void> showRequestDialog({
    required BuildContext context,
    required String requestId,
    required String requestType,
    required Map<String, dynamic> requestData,
    DialogPriority priority = DialogPriority.normal,
    DateTime? expiresAt,
  }) async {
    try {
      LoggingService.info(
        'EnhancedDialogManager: Showing request dialog: $requestId ($requestType)',
      );

      // Validate input parameters
      if (requestId.isEmpty || requestType.isEmpty) {
        LoggingService.error(
          'EnhancedDialogManager: Invalid request parameters - requestId: $requestId, requestType: $requestType',
        );
        return;
      }

      // Check for duplicate dialogs
      if (await _isDuplicateDialog(requestId, requestType)) {
        LoggingService.info(
          'EnhancedDialogManager: Duplicate dialog prevented: $requestId',
        );
        return;
      }

      // Check cooldown period
      if (_isInCooldownPeriod(requestId, requestType)) {
        LoggingService.info(
          'EnhancedDialogManager: Dialog in cooldown period: $requestId',
        );
        return;
      }

      // Mark dialog as active to prevent duplicates
      final dialogKey = _createDialogKey(requestId, requestType);
      _activeDialogs[dialogKey] = true;
      _dialogTimestamps[dialogKey] = DateTime.now();

      try {
        // Mark dialog as shown in persistent storage
        await RequestDialogStateManager.markDialogShown(requestId, requestType);

        // Remove from pending dialogs if it exists
        await RequestDialogStateManager.removePendingDialog(requestId, requestType);

        // Save to state restoration
        await RequestStateRestorationService.saveLastShownRequest(requestId, requestType);

        // Show the appropriate dialog based on type
        await _showDialogByType(context, requestType, requestData);

        LoggingService.success(
          'EnhancedDialogManager: Successfully showed dialog: $requestId',
        );
      } finally {
        // Always clean up active dialog tracking
        _activeDialogs.remove(dialogKey);
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'EnhancedDialogManager: Error showing request dialog: $e',
        stackTrace: stackTrace,
      );

      // On error, increment retry count for potential background retry
      await RequestDialogStateManager.incrementDialogRetry(requestId, requestType);
    }
  }

  /// Add a request to pending dialogs for later display
  static Future<void> addPendingDialog({
    required String requestId,
    required String requestType,
    required Map<String, dynamic> requestData,
    DialogPriority priority = DialogPriority.normal,
    DateTime? expiresAt,
  }) async {
    try {
      LoggingService.info(
        'EnhancedDialogManager: Adding pending dialog: $requestId ($requestType)',
      );

      // Check if dialog has already been shown
      final hasBeenShown = await RequestDialogStateManager.hasDialogBeenShown(
        requestId,
        requestType,
      );

      if (hasBeenShown) {
        LoggingService.info(
          'EnhancedDialogManager: Dialog already shown, not adding to pending: $requestId',
        );
        return;
      }

      // Add to pending dialogs
      await RequestDialogStateManager.addPendingDialog(
        requestId,
        requestType,
        requestData,
        priority: priority,
        expiresAt: expiresAt ?? DateTime.now().add(const Duration(hours: 24)),
      );

      // Save state for restoration
      await RequestStateRestorationService.saveRequestState();

      // Schedule background notification if app is not active
      final dialog = PendingDialog(
        requestId: requestId,
        requestType: requestType,
        data: requestData,
        timestamp: DateTime.now(),
        priority: priority,
        expiresAt: expiresAt,
      );

      await BackgroundRequestProcessor.scheduleRequestNotification(dialog);

      LoggingService.success(
        'EnhancedDialogManager: Added pending dialog with background notification: $requestId',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'EnhancedDialogManager: Error adding pending dialog: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Process all pending dialogs
  static Future<void> processPendingDialogs(BuildContext context) async {
    try {
      LoggingService.info('EnhancedDialogManager: Processing pending dialogs...');

      final pendingDialogs = await RequestDialogStateManager.getPendingDialogs();

      if (pendingDialogs.isEmpty) {
        LoggingService.info('EnhancedDialogManager: No pending dialogs to process');
        return;
      }

      LoggingService.info(
        'EnhancedDialogManager: Found ${pendingDialogs.length} pending dialogs',
      );

      // Process dialogs in priority order
      for (final dialog in pendingDialogs) {
        await showRequestDialog(
          context: context,
          requestId: dialog.requestId,
          requestType: dialog.requestType,
          requestData: dialog.data,
          priority: dialog.priority,
          expiresAt: dialog.expiresAt,
        );

        // Add small delay between dialogs to prevent overwhelming the user
        await Future.delayed(const Duration(milliseconds: 500));
      }

      LoggingService.success('EnhancedDialogManager: Completed processing pending dialogs');
    } catch (e, stackTrace) {
      LoggingService.error(
        'EnhancedDialogManager: Error processing pending dialogs: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Get dialog management metrics
  static Future<Map<String, dynamic>> getDialogMetrics() async {
    try {
      final dialogMetrics = await RequestDialogStateManager.getDialogMetrics();
      final restorationMetrics = await RequestStateRestorationService.getRestorationMetrics();

      return {
        'dialog_metrics': dialogMetrics,
        'restoration_metrics': restorationMetrics,
        'active_dialogs': _activeDialogs.length,
        'dialog_timestamps': _dialogTimestamps.length,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error('EnhancedDialogManager: Failed to get metrics: $e');
      return <String, dynamic>{};
    }
  }

  /// Clear all dialog state (for testing or user logout)
  static Future<void> clearAllDialogState() async {
    try {
      await RequestDialogStateManager.clearAllDialogState();
      await RequestStateRestorationService.clearRestorationData();
      
      _activeDialogs.clear();
      _dialogTimestamps.clear();

      LoggingService.info('EnhancedDialogManager: Cleared all dialog state');
    } catch (e, stackTrace) {
      LoggingService.error(
        'EnhancedDialogManager: Failed to clear dialog state: $e',
        stackTrace: stackTrace,
      );
    }
  }

  // Private helper methods

  static Future<bool> _isDuplicateDialog(String requestId, String requestType) async {
    // Check if dialog is currently active
    final dialogKey = _createDialogKey(requestId, requestType);
    if (_activeDialogs[dialogKey] == true) {
      return true;
    }

    // Check if dialog has been shown before
    return await RequestDialogStateManager.hasDialogBeenShown(requestId, requestType);
  }

  static bool _isInCooldownPeriod(String requestId, String requestType) {
    final dialogKey = _createDialogKey(requestId, requestType);
    final lastShown = _dialogTimestamps[dialogKey];
    
    if (lastShown == null) return false;
    
    return DateTime.now().difference(lastShown) < _dialogCooldown;
  }

  static String _createDialogKey(String requestId, String requestType) {
    return '${requestType}_$requestId';
  }

  static Future<void> _showDialogByType(
    BuildContext context,
    String requestType,
    Map<String, dynamic> requestData,
  ) async {
    // This method would delegate to the appropriate dialog implementation
    // in the presentation layer, respecting the four-layer dependency rule
    
    LoggingService.info(
      'EnhancedDialogManager: Delegating dialog display to presentation layer: $requestType',
    );

    // The actual dialog implementation should be handled by the presentation layer
    // This service only manages the state and coordination
    
    // For now, we'll log that the dialog should be shown
    // The presentation layer will handle the actual dialog display
    LoggingService.info(
      'EnhancedDialogManager: Dialog ready for display - Type: $requestType, Data: $requestData',
    );
  }
}

/// Extension methods for dialog priority handling
extension DialogPriorityExtensions on DialogPriority {
  /// Get the display order for this priority (lower numbers = higher priority)
  int get displayOrder {
    switch (this) {
      case DialogPriority.urgent:
        return 0;
      case DialogPriority.high:
        return 1;
      case DialogPriority.normal:
        return 2;
      case DialogPriority.low:
        return 3;
    }
  }

  /// Get a human-readable description of this priority
  String get description {
    switch (this) {
      case DialogPriority.urgent:
        return 'Urgent - Show immediately';
      case DialogPriority.high:
        return 'High - Show as soon as possible';
      case DialogPriority.normal:
        return 'Normal - Show in regular order';
      case DialogPriority.low:
        return 'Low - Show when convenient';
    }
  }
}
