import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pending_dialog_model.g.dart';

/// Model representing a pending dialog that needs to be shown to the user
@JsonSerializable()
class PendingDialog extends Equatable {
  const PendingDialog({
    required this.requestId,
    required this.requestType,
    required this.data,
    required this.timestamp,
    this.priority = DialogPriority.normal,
    this.expiresAt,
    this.retryCount = 0,
    this.maxRetries = 3,
  });

  factory PendingDialog.fromJson(Map<String, dynamic> json) =>
      _$PendingDialogFromJson(json);

  factory PendingDialog.fromMap(Map<String, dynamic> map) => PendingDialog(
        requestId: map['request_id'] as String,
        requestType: map['request_type'] as String,
        data: Map<String, dynamic>.from(map['data'] as Map),
        timestamp: DateTime.parse(map['timestamp'] as String),
        priority: DialogPriority.values.firstWhere(
          (e) => e.name == map['priority'],
          orElse: () => DialogPriority.normal,
        ),
        expiresAt: map['expires_at'] != null
            ? DateTime.parse(map['expires_at'] as String)
            : null,
        retryCount: map['retry_count'] as int? ?? 0,
        maxRetries: map['max_retries'] as int? ?? 3,
      );

  /// Unique identifier for the request
  final String requestId;

  /// Type of request (contact_request, bubble_start_request, etc.)
  final String requestType;

  /// Request data payload
  final Map<String, dynamic> data;

  /// When the dialog was created/scheduled
  final DateTime timestamp;

  /// Priority level for dialog display
  final DialogPriority priority;

  /// When the dialog expires (optional)
  final DateTime? expiresAt;

  /// Number of times we've attempted to show this dialog
  final int retryCount;

  /// Maximum number of retry attempts
  final int maxRetries;

  /// Check if this dialog has expired
  bool get isExpired =>
      expiresAt != null && DateTime.now().isAfter(expiresAt!);

  /// Check if we've exceeded retry attempts
  bool get hasExceededRetries => retryCount >= maxRetries;

  /// Check if this dialog should still be shown
  bool get isValid => !isExpired && !hasExceededRetries;

  /// Create a copy with incremented retry count
  PendingDialog withIncrementedRetry() => copyWith(
        retryCount: retryCount + 1,
      );

  /// Create a copy with updated fields
  PendingDialog copyWith({
    String? requestId,
    String? requestType,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    DialogPriority? priority,
    DateTime? expiresAt,
    int? retryCount,
    int? maxRetries,
  }) =>
      PendingDialog(
        requestId: requestId ?? this.requestId,
        requestType: requestType ?? this.requestType,
        data: data ?? this.data,
        timestamp: timestamp ?? this.timestamp,
        priority: priority ?? this.priority,
        expiresAt: expiresAt ?? this.expiresAt,
        retryCount: retryCount ?? this.retryCount,
        maxRetries: maxRetries ?? this.maxRetries,
      );

  Map<String, dynamic> toMap() => {
        'request_id': requestId,
        'request_type': requestType,
        'data': data,
        'timestamp': timestamp.toIso8601String(),
        'priority': priority.name,
        'expires_at': expiresAt?.toIso8601String(),
        'retry_count': retryCount,
        'max_retries': maxRetries,
      };

  Map<String, dynamic> toJson() => _$PendingDialogToJson(this);

  @override
  List<Object?> get props => [
        requestId,
        requestType,
        data,
        timestamp,
        priority,
        expiresAt,
        retryCount,
        maxRetries,
      ];

  @override
  String toString() => 'PendingDialog('
      'requestId: $requestId, '
      'requestType: $requestType, '
      'priority: $priority, '
      'retryCount: $retryCount, '
      'isValid: $isValid)';
}

/// Priority levels for dialog display
enum DialogPriority {
  low,
  normal,
  high,
  urgent,
}

/// Extension to get priority sorting order
extension DialogPriorityExtension on DialogPriority {
  int get sortOrder {
    switch (this) {
      case DialogPriority.urgent:
        return 0;
      case DialogPriority.high:
        return 1;
      case DialogPriority.normal:
        return 2;
      case DialogPriority.low:
        return 3;
    }
  }
}
