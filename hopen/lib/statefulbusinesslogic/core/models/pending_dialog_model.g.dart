// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pending_dialog_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PendingDialog _$PendingDialogFromJson(Map<String, dynamic> json) =>
    PendingDialog(
      requestId: json['requestId'] as String,
      requestType: json['requestType'] as String,
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      priority:
          $enumDecodeNullable(_$DialogPriorityEnumMap, json['priority']) ??
              DialogPriority.normal,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 3,
    );

Map<String, dynamic> _$PendingDialogToJson(PendingDialog instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'requestType': instance.requestType,
      'data': instance.data,
      'timestamp': instance.timestamp.toIso8601String(),
      'priority': _$DialogPriorityEnumMap[instance.priority]!,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'retryCount': instance.retryCount,
      'maxRetries': instance.maxRetries,
    };

const _$DialogPriorityEnumMap = {
  DialogPriority.low: 'low',
  DialogPriority.normal: 'normal',
  DialogPriority.high: 'high',
  DialogPriority.urgent: 'urgent',
};
