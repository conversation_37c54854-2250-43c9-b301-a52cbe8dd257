#!/bin/bash

# Flutter App Configuration Update Script for Development Certificates
# This script updates the Flutter app to use the new development domain

set -e

echo "📱 Updating Flutter app configuration for development certificates..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="hopen.local"
IP="*********"
CONFIG_FILE="lib/config/app_config.dart"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Backup original config
backup_config() {
    print_status "Creating backup of app configuration..."
    cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    print_success "Backup created"
}

# Update app configuration
update_app_config() {
    print_status "Updating app configuration to use development domain..."
    
    # Create temporary file with updated configuration
    cat > temp_config.dart << EOF
import 'package:flutter/foundation.dart';

/// Production-ready application configuration
/// Handles environment-specific settings and feature flags
class AppConfig {
  // Test environment state
  static bool _isTestEnvironment = false;

  // Environment configuration
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: kDebugMode ? 'development' : 'production',
  );

  // Network IP for device connection to Docker backend (overridable via --dart-define=DOCKER_HOST_IP)
  static const String dockerHostIP = String.fromEnvironment(
    'DOCKER_HOST_IP',
    defaultValue: '$IP',
  );

  // Development domain for proper certificates
  static const String developmentDomain = String.fromEnvironment(
    'DEV_DOMAIN',
    defaultValue: '$DOMAIN',
  );

  // API Configuration with proper domain support
  static const String apiBaseUrl = environment == 'production'
      ? 'https://api.hopenapp.com'
      : environment == 'staging'
          ? 'https://staging-api.hopenapp.com'
          : 'https://\$developmentDomain:8443';  // Use domain for development

  static const String backendUrl = apiBaseUrl; // Alias for compatibility

  static String get apiHost {
    final uri = Uri.parse(apiBaseUrl);
    return uri.host;
  }

  static int get apiPort {
    final uri = Uri.parse(apiBaseUrl);
    return uri.port;
  }

  static const String websocketUrl =
      environment == 'production'
          ? 'wss://ws.hopenapp.com'
          : environment == 'staging'
          ? 'wss://staging-ws.hopenapp.com'
          : 'wss://\$developmentDomain:8443/ws';  // Use domain for development

  // Ory Stack Configuration (Identity Management)
  static const String oryKratosPublicUrl = String.fromEnvironment(
    'ORY_KRATOS_PUBLIC_URL',
    defaultValue:
        environment == 'production'
            ? 'https://auth.hopenapp.com'
            : 'http://\$dockerHostIP:4433',  // Kratos on HTTP
  );

  static const String oryKratosAdminUrl = String.fromEnvironment(
    'ORY_KRATOS_ADMIN_URL',
    defaultValue:
        environment == 'production'
            ? 'https://auth-admin.hopenapp.com'
            : 'http://\$dockerHostIP:4434',  // Kratos Admin on HTTP
  );

  static const String oryHydraPublicUrl = String.fromEnvironment(
    'ORY_HYDRA_PUBLIC_URL',
    defaultValue:
        environment == 'production'
            ? 'https://oauth.hopenapp.com'
            : 'http://\$dockerHostIP:4444',
  );

  static const String oryHydraAdminUrl = String.fromEnvironment(
    'ORY_HYDRA_ADMIN_URL',
    defaultValue:
        environment == 'production'
            ? 'https://oauth-admin.hopenapp.com'
            : 'http://\$dockerHostIP:4445',
  );
EOF

    # Add the rest of the configuration (keeping existing content)
    tail -n +80 "$CONFIG_FILE" >> temp_config.dart
    
    # Replace the original file
    mv temp_config.dart "$CONFIG_FILE"
    
    print_success "App configuration updated to use development domain"
}

# Create simplified Flutter environment file
create_dev_env() {
    print_status "Creating Flutter development environment configuration..."

    cat > .env.development << EOF
# Flutter Development Environment Configuration
# Simplified configuration that works with unified backend setup
ENVIRONMENT=development
DOCKER_HOST_IP=$IP
TLS_DOMAIN=$DOMAIN

# API Configuration (external access via DOCKER_HOST_IP)
API_BASE_URL=https://$DOMAIN:8443
BACKEND_URL=https://$DOMAIN:8443
WEBSOCKET_URL=wss://$DOMAIN:8443/ws

# Ory Stack Configuration (external access)
KRATOS_PUBLIC_URL=http://$IP:4433
KRATOS_ADMIN_URL=http://$IP:4434
HYDRA_PUBLIC_URL=http://$IP:4444
HYDRA_ADMIN_URL=http://$IP:4445

# MQTT Configuration (external access)
MQTT_BROKER_URL=$IP
MQTT_PORT=1883
MQTT_SECURE_PORT=8883
MQTT_WS_PORT=8083
MQTT_WS_URL=ws://$IP:8083/mqtt
MQTT_USERNAME=hopen_user
MQTT_PASSWORD=hopen_password

# MinIO Configuration (external access)
MINIO_ENDPOINT=$IP:9000
MINIO_ACCESS_KEY=hopen
MINIO_SECRET_KEY=hopen123
MINIO_BUCKET=hopen-storage
MINIO_USE_SSL=false

# Feature Flags
ENABLE_HTTP3=true
ENABLE_DEBUG_LOGGING=true
ENABLE_ANALYTICS=false
ENABLE_CRASH_REPORTING=false
EOF

    print_success "Development environment file created"
}

# Update HTTP/3 client configuration
update_http3_config() {
    print_status "Updating HTTP/3 client configuration..."
    
    # The HTTP/3 client should now work with proper certificates
    # No changes needed to the client code, just ensure it uses the domain
    
    print_success "HTTP/3 client configuration is ready for proper certificates"
}

# Display next steps
show_next_steps() {
    echo ""
    print_success "🎉 Flutter app configuration updated!"
    echo ""
    echo "Next steps:"
    echo ""
    echo "1. 🔄 Hot restart your Flutter app:"
    echo "   flutter run --dart-define=ENVIRONMENT=development --dart-define=DEV_DOMAIN=$DOMAIN"
    echo ""
    echo "2. 🧪 Test the new configuration:"
    echo "   - App should now connect to https://$DOMAIN:8443"
    echo "   - HTTP/3 should work with proper certificates"
    echo "   - Check logs for 'HTTP/3' instead of 'HTTP/2 (Certificate Bypass)'"
    echo ""
    echo "3. 🔍 Verify in app logs:"
    echo "   Look for: 'HTTP/3 client service initialized with HTTP/3 support'"
    echo ""
    echo "Configuration changes:"
    echo "  📱 App Config: $CONFIG_FILE"
    echo "  🌍 Environment: .env.development"
    echo "  🔒 Domain: $DOMAIN"
    echo "  📍 IP Fallback: $IP"
    echo ""
    echo "Backup created: $CONFIG_FILE.backup.*"
    echo ""
    print_warning "If you need to revert: cp $CONFIG_FILE.backup.* $CONFIG_FILE"
}

# Main execution
main() {
    echo "🚀 Starting Flutter app configuration update..."
    echo ""
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "App configuration file not found: $CONFIG_FILE"
        print_error "Please run this script from the Flutter app root directory"
        exit 1
    fi
    
    backup_config
    update_app_config
    create_dev_env
    update_http3_config
    show_next_steps
}

# Run main function
main "$@"
