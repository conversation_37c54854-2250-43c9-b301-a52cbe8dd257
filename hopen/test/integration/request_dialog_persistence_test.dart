import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

import 'package:hopen/statefulbusinesslogic/core/services/request_dialog_state_manager.dart';
import 'package:hopen/statefulbusinesslogic/core/services/mqtt_message_persistence.dart';
import 'package:hopen/statefulbusinesslogic/core/services/background_request_processor.dart';
import 'package:hopen/statefulbusinesslogic/core/services/request_state_restoration_service.dart';
import 'package:hopen/statefulbusinesslogic/core/services/enhanced_dialog_manager.dart';
import 'package:hopen/statefulbusinesslogic/core/models/pending_dialog_model.dart';

/// Comprehensive integration test for request dialog persistence and recovery
/// 
/// This test verifies that all components work together to ensure
/// request dialogs are never lost across various failure scenarios.
void main() {
  group('Request Dialog Persistence Integration Tests', () {
    setUp(() async {
      // Clear all shared preferences before each test
      SharedPreferences.setMockInitialValues({});
      
      // Initialize services
      await RequestDialogStateManager.initialize();
      await RequestStateRestorationService.initialize();
    });

    tearDown(() async {
      // Clean up after each test
      await RequestDialogStateManager.clearAllDialogState();
      await RequestStateRestorationService.clearRestorationData();
      await MqttMessagePersistence.clearAllMessages();
    });

    testWidgets('Complete request dialog lifecycle', (WidgetTester tester) async {
      // Test data
      const requestId = 'test_request_123';
      const requestType = 'contact_request_received';
      final requestData = {
        'senderName': 'John Doe',
        'senderId': 'user_456',
        'message': 'Hello, let\'s connect!',
      };

      // 1. Add pending dialog
      await EnhancedDialogManager.addPendingDialog(
        requestId: requestId,
        requestType: requestType,
        requestData: requestData,
        priority: DialogPriority.high,
      );

      // Verify dialog was added to pending list
      final pendingDialogs = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingDialogs.length, equals(1));
      expect(pendingDialogs.first.requestId, equals(requestId));
      expect(pendingDialogs.first.requestType, equals(requestType));
      expect(pendingDialogs.first.priority, equals(DialogPriority.high));

      // 2. Verify dialog hasn't been shown yet
      final hasBeenShown = await RequestDialogStateManager.hasDialogBeenShown(
        requestId,
        requestType,
      );
      expect(hasBeenShown, isFalse);

      // 3. Mark dialog as shown (simulating user interaction)
      await RequestDialogStateManager.markDialogShown(requestId, requestType);

      // Verify dialog is now marked as shown
      final hasBeenShownAfter = await RequestDialogStateManager.hasDialogBeenShown(
        requestId,
        requestType,
      );
      expect(hasBeenShownAfter, isTrue);

      // 4. Verify dialog is removed from pending list
      await RequestDialogStateManager.removePendingDialog(requestId, requestType);
      final pendingDialogsAfter = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingDialogsAfter.length, equals(0));
    });

    test('MQTT message persistence and recovery', () async {
      // Test data
      const topic = 'user/123/notifications';
      const payload = '{"type": "contact_request_received", "requestId": "req_789"}';

      // 1. Store unprocessed message
      await MqttMessagePersistence.storeUnprocessedMessage(topic, payload, priority: 1);

      // 2. Retrieve unprocessed messages
      final unprocessedMessages = await MqttMessagePersistence.getUnprocessedMessages();
      expect(unprocessedMessages.length, equals(1));
      expect(unprocessedMessages.first.topic, equals(topic));
      expect(unprocessedMessages.first.payload, equals(payload));
      expect(unprocessedMessages.first.priority, equals(1));

      // 3. Mark message as processed
      await MqttMessagePersistence.markMessageProcessed(unprocessedMessages.first.id);

      // 4. Verify message is removed
      final messagesAfter = await MqttMessagePersistence.getUnprocessedMessages();
      expect(messagesAfter.length, equals(0));
    });

    test('Dialog state restoration', () async {
      // Test data
      const requestId = 'restore_test_456';
      const requestType = 'bubble_start_request_received';
      final requestData = {
        'bubbleName': 'Test Bubble',
        'senderName': 'Jane Smith',
      };

      // 1. Add pending dialog
      await RequestDialogStateManager.addPendingDialog(
        requestId,
        requestType,
        requestData,
        priority: DialogPriority.normal,
      );

      // 2. Save state for restoration
      await RequestStateRestorationService.saveRequestState();

      // 3. Clear current state (simulating app termination)
      await RequestDialogStateManager.clearAllDialogState();

      // Verify state is cleared
      final pendingAfterClear = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingAfterClear.length, equals(0));

      // 4. Restore state
      await RequestStateRestorationService.restoreRequestState();

      // 5. Verify state is restored
      final pendingAfterRestore = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingAfterRestore.length, equals(1));
      expect(pendingAfterRestore.first.requestId, equals(requestId));
      expect(pendingAfterRestore.first.requestType, equals(requestType));
    });

    test('Dialog priority ordering', () async {
      // Add dialogs with different priorities
      await RequestDialogStateManager.addPendingDialog(
        'low_priority',
        'contact_request_received',
        {'priority': 'low'},
        priority: DialogPriority.low,
      );

      await RequestDialogStateManager.addPendingDialog(
        'urgent_priority',
        'contact_request_received',
        {'priority': 'urgent'},
        priority: DialogPriority.urgent,
      );

      await RequestDialogStateManager.addPendingDialog(
        'normal_priority',
        'contact_request_received',
        {'priority': 'normal'},
        priority: DialogPriority.normal,
      );

      await RequestDialogStateManager.addPendingDialog(
        'high_priority',
        'contact_request_received',
        {'priority': 'high'},
        priority: DialogPriority.high,
      );

      // Get pending dialogs (should be sorted by priority)
      final pendingDialogs = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingDialogs.length, equals(4));

      // Verify priority ordering (urgent -> high -> normal -> low)
      expect(pendingDialogs[0].priority, equals(DialogPriority.urgent));
      expect(pendingDialogs[1].priority, equals(DialogPriority.high));
      expect(pendingDialogs[2].priority, equals(DialogPriority.normal));
      expect(pendingDialogs[3].priority, equals(DialogPriority.low));
    });

    test('Dialog expiration handling', () async {
      // Add dialog that expires in 1 second
      final expiresAt = DateTime.now().add(const Duration(seconds: 1));
      
      await RequestDialogStateManager.addPendingDialog(
        'expiring_dialog',
        'contact_request_received',
        {'test': 'data'},
        expiresAt: expiresAt,
      );

      // Verify dialog is initially present
      final pendingBefore = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingBefore.length, equals(1));

      // Wait for expiration
      await Future.delayed(const Duration(seconds: 2));

      // Verify expired dialog is filtered out
      final pendingAfter = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingAfter.length, equals(0));
    });

    test('Dialog retry mechanism', () async {
      const requestId = 'retry_test';
      const requestType = 'contact_request_received';

      // Add pending dialog
      await RequestDialogStateManager.addPendingDialog(
        requestId,
        requestType,
        {'test': 'data'},
      );

      // Get initial dialog
      final initialDialogs = await RequestDialogStateManager.getPendingDialogs();
      expect(initialDialogs.first.retryCount, equals(0));

      // Increment retry count
      await RequestDialogStateManager.incrementDialogRetry(requestId, requestType);

      // Verify retry count increased
      final afterRetry = await RequestDialogStateManager.getPendingDialogs();
      expect(afterRetry.first.retryCount, equals(1));

      // Increment until max retries exceeded
      for (int i = 0; i < 3; i++) {
        await RequestDialogStateManager.incrementDialogRetry(requestId, requestType);
      }

      // Verify dialog is filtered out when max retries exceeded
      final afterMaxRetries = await RequestDialogStateManager.getPendingDialogs();
      expect(afterMaxRetries.length, equals(0));
    });

    test('Duplicate dialog prevention', () async {
      const requestId = 'duplicate_test';
      const requestType = 'contact_request_received';
      final requestData = {'test': 'data'};

      // Mark dialog as shown
      await RequestDialogStateManager.markDialogShown(requestId, requestType);

      // Try to add the same dialog as pending
      await EnhancedDialogManager.addPendingDialog(
        requestId: requestId,
        requestType: requestType,
        requestData: requestData,
      );

      // Verify dialog was not added to pending list
      final pendingDialogs = await RequestDialogStateManager.getPendingDialogs();
      expect(pendingDialogs.length, equals(0));
    });

    test('Metrics collection', () async {
      // Add some dialogs and mark them as shown
      await RequestDialogStateManager.addPendingDialog(
        'metrics_test_1',
        'contact_request_received',
        {'test': 'data'},
      );

      await RequestDialogStateManager.markDialogShown(
        'metrics_test_1',
        'contact_request_received',
      );

      // Get metrics
      final metrics = await EnhancedDialogManager.getDialogMetrics();
      
      expect(metrics, isNotEmpty);
      expect(metrics['dialog_metrics'], isNotNull);
      expect(metrics['last_updated'], isNotNull);
    });
  });
}
