import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../lib/provider/services/user_data_isolation_service.dart';
import '../../../../lib/provider/notifiers/user_profile_notifier.dart';
import '../../../../lib/provider/repositories/user/cached_user_repository.dart';
import '../../../../lib/statefulbusinesslogic/core/db/app_database.dart';
import '../../../../lib/di/injection_container_refactored.dart' as di;

// Generate mocks
@GenerateMocks([
  UserProfileNotifier,
  CachedUserRepository,
  AppDatabase,
  UserProfileDao,
  ProfilePictureDao,
])
import 'user_data_isolation_service_test.mocks.dart';

void main() {
  group('UserDataIsolationService', () {
    late MockUserProfileNotifier mockUserProfileNotifier;
    late MockCachedUserRepository mockCachedUserRepository;
    late MockAppDatabase mockDatabase;
    late MockUserProfileDao mockUserProfileDao;
    late MockProfilePictureDao mockProfilePictureDao;

    setUp(() {
      mockUserProfileNotifier = MockUserProfileNotifier();
      mockCachedUserRepository = MockCachedUserRepository();
      mockDatabase = MockAppDatabase();
      mockUserProfileDao = MockUserProfileDao();
      mockProfilePictureDao = MockProfilePictureDao();

      // Setup database DAOs
      when(mockDatabase.userProfileDao).thenReturn(mockUserProfileDao);
      when(mockDatabase.profilePictureDao).thenReturn(mockProfilePictureDao);

      // Mock successful operations
      when(mockCachedUserRepository.clearCache()).thenAnswer((_) async {});
      when(mockUserProfileDao.clearAllProfiles()).thenAnswer((_) async {});
      when(mockProfilePictureDao.clearAllCache()).thenAnswer((_) async {});
      when(mockUserProfileDao.getAllProfiles()).thenAnswer((_) async => []);
    });

    testWidgets('clearAllUserData should clear all user-specific data', (tester) async {
      // Setup SharedPreferences
      SharedPreferences.setMockInitialValues({
        'user_specific_key': 'user_data',
        'app_version': '1.0.0', // This should be preserved
        'session_token': 'abc123',
        'language_preference': 'en', // This should be preserved
      });

      // Register mocks in DI container
      di.sl.registerSingleton<UserProfileNotifier>(mockUserProfileNotifier);
      di.sl.registerSingleton<CachedUserRepository>(mockCachedUserRepository);
      di.sl.registerSingleton<AppDatabase>(mockDatabase);

      // Act
      await UserDataIsolationService.clearAllUserData(reason: 'Test logout');

      // Assert
      verify(mockUserProfileNotifier.clearUser()).called(1);
      verify(mockCachedUserRepository.clearCache()).called(1);
      verify(mockUserProfileDao.clearAllProfiles()).called(1);
      verify(mockProfilePictureDao.clearAllCache()).called(1);

      // Verify SharedPreferences clearing
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString('user_specific_key'), isNull);
      expect(prefs.getString('session_token'), isNull);
      expect(prefs.getString('app_version'), equals('1.0.0')); // Should be preserved
      expect(prefs.getString('language_preference'), equals('en')); // Should be preserved
    });

    testWidgets('verifyDataClearing should return correct verification results', (tester) async {
      // Setup
      when(mockUserProfileNotifier.currentUser).thenReturn(null);
      when(mockUserProfileDao.getAllProfiles()).thenAnswer((_) async => []);

      // Register mocks
      di.sl.registerSingleton<UserProfileNotifier>(mockUserProfileNotifier);
      di.sl.registerSingleton<AppDatabase>(mockDatabase);

      // Act
      final results = await UserDataIsolationService.verifyDataClearing();

      // Assert
      expect(results['user_profile_notifier_cleared'], isTrue);
      expect(results['drift_user_profiles_cleared'], isTrue);
    });

    testWidgets('clearAllUserData should handle errors gracefully', (tester) async {
      // Setup error scenarios
      when(mockUserProfileNotifier.clearUser()).thenThrow(Exception('Test error'));
      when(mockCachedUserRepository.clearCache()).thenThrow(Exception('Cache error'));

      // Register mocks
      di.sl.registerSingleton<UserProfileNotifier>(mockUserProfileNotifier);
      di.sl.registerSingleton<CachedUserRepository>(mockCachedUserRepository);
      di.sl.registerSingleton<AppDatabase>(mockDatabase);

      // Act & Assert - should not throw
      expect(
        () => UserDataIsolationService.clearAllUserData(reason: 'Test error handling'),
        returnsNormally,
      );
    });

    tearDown(() {
      // Clean up DI container
      if (di.sl.isRegistered<UserProfileNotifier>()) {
        di.sl.unregister<UserProfileNotifier>();
      }
      if (di.sl.isRegistered<CachedUserRepository>()) {
        di.sl.unregister<CachedUserRepository>();
      }
      if (di.sl.isRegistered<AppDatabase>()) {
        di.sl.unregister<AppDatabase>();
      }
    });
  });
}
